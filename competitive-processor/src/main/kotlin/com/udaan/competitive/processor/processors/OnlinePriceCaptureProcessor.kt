package com.udaan.competitive.processor.processors

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.competitive.core.dao.AggregationType
import com.udaan.competitive.core.dao.CompetitiveDataRepository
import com.udaan.competitive.core.dao.CompetitorCatalogRepository
import com.udaan.competitive.core.dao.InsightRulesDao
import com.udaan.competitive.core.dao.OnlineCompSessionDao
import com.udaan.competitive.core.dao.ProductInsightRule
import com.udaan.competitive.core.integrations.NetworkProvider
import com.udaan.competitive.core.manager.PricingManager
import com.udaan.competitive.core.models.OnlinePriceCapturePayload
import com.udaan.competitive.core.models.ProcessorQueues
import com.udaan.competitive.core.utils.TimeHelpers
import com.udaan.competitive.core.utils.getClosestToMedian
import com.udaan.competitive.core.utils.getCompNCity
import com.udaan.competitive.core.utils.getMode
import com.udaan.competitive.core.utils.getUniqueByUserWithLatestData
import com.udaan.competitive.models.CompetitiveData
import com.udaan.competitive.models.CompetitorSpecificAttributes
import com.udaan.competitive.models.JTSessionAttributes
import com.udaan.competitive.processor.OnQueue
import com.udaan.instrumentation.TelemetryScope
import kotlinx.coroutines.Deferred
import kotlin.time.ExperimentalTime

@Singleton
@OnQueue(ProcessorQueues.OnlinePriceCaptureQueue)
class OnlinePriceCaptureProcessor @Inject constructor(
    override val mapper: ObjectMapper,
    private val insightRulesDao: InsightRulesDao,
    private val competitiveDataRepository: CompetitiveDataRepository,
    private val pricingManager: PricingManager,
    private val competitorCatalogRepository: CompetitorCatalogRepository,
    private val onlineCompSessionDao: OnlineCompSessionDao,
    private val networkProvider: NetworkProvider,
) : BaseProcessor<OnlinePriceCapturePayload>(OnlinePriceCapturePayload::class.java, mapper) {

    companion object {
        private val logger by logger()
    }

    override suspend fun processMessage(message: OnlinePriceCapturePayload) {
        logger.info("Processing online price capture for ${message.id} ${message.compProductId}")
        val (competitor, city) = message.competitorCityId.getCompNCity()
        
        val insightRules = insightRulesDao.getInsightRules(message.compProductId, city, competitor)
        insightRules.groupBy { it.aggregationType }
            .forEach {  (aggregationType, rules) ->
                when(aggregationType) {
                    AggregationType.MODE_ACROSS_ACCOUNTS -> handleModeAcrossAccounts(rules, message.compProductId)
                    AggregationType.MODE_OF_MIN_IN_ACCOUNTS -> handleModeOfMin(rules)
                }
            }
    }

    @OptIn(ExperimentalTime::class)
    private suspend fun handleModeAcrossAccounts(
        insights: List<ProductInsightRule>,
        compProductId: String
    ) {
        val dataPoints = competitiveDataRepository.getDataPointsInWindow(
            compProductId = compProductId,
            startAt = TimeHelpers.getNow().minusMinutes(30),
            endAt = TimeHelpers.getNow()
        )
        
        val uniqueByUserWithLatestData = dataPoints.getUniqueByUserWithLatestData()
        val modeDataPoint = dataPoints.getMode()
        val closestToMedianDataPoint by lazy { dataPoints.getClosestToMedian() }
        insights.parallelMap { insight ->

            if (insight.minDataPoints > uniqueByUserWithLatestData.size) {
                logger.info("Not enough data points for compProductId: $compProductId, skipping...")
                return@parallelMap
            }

            val finalDataPoint = modeDataPoint ?: closestToMedianDataPoint ?: dataPoints.firstOrNull()

            if (finalDataPoint == null) {
                logger.info("No data points for compProductId: $compProductId, skipping...")
            } else {
                pricingManager.generateOnlineCompPricingSignal(
                    udCatalogId = insight.udCatalogId,
                    competitiveData = finalDataPoint,
                    city = insight.city,
                    competitor = insight.competitor,
                    mappingType = insight.mappingType,
                    compProduct = competitorCatalogRepository.getById(insight.compProductId)
                )
            }
            generateTerritorySignal(
                insight = insight,
                dataPoints = uniqueByUserWithLatestData
            )
        }
    }

    private suspend fun generateTerritorySignal(
        insight: ProductInsightRule,
        dataPoints: Collection<CompetitiveData>
    ) {

        val territoryDataPoints = dataPoints.groupBy {
            val userId = it.competitorSpecificAttributes?.get(CompetitorSpecificAttributes.USER_ID.key)
            val session = userId?.let { onlineCompSessionDao.getLatestActiveByAccountId(userId) }
            val location = (session?.competitorSpecificAttributes as? JTSessionAttributes)?.location
            val territoryId = location?.let {
                networkProvider.getTerritoryIdForCompetitor(
                    competitor = insight.competitor,
                    latitude = location.lat,
                    longitude = location.lon,
                    accountId = userId
                )
            }
            territoryId
        }

        territoryDataPoints.forEach { (territoryId, dataPoints) ->
            if (territoryId == null) {
                logger.info("${dataPoints.size} data points without territoryId for compProductId: ${insight.compProductId}, skipping...")
                return@forEach
            }
            val mode = dataPoints.getMode()
            val median = dataPoints.getClosestToMedian()
            val finalDataPoint = mode ?: median
            if (finalDataPoint == null) {
                logger.info("No data points for territoryId: $territoryId, skipping...")
                return@forEach
            }
            pricingManager.generateOnlineCompPricingSignal(
                udCatalogId = insight.udCatalogId,
                competitiveData = finalDataPoint,
                jtPolygonId = territoryId,
                competitor = insight.competitor,
                mappingType = insight.mappingType,
                compProduct = competitorCatalogRepository.getById(insight.compProductId)
            )
        }
    }

    private fun handleModeOfMin(
        modeOfMinRules: List<ProductInsightRule>
    ): Deferred<Unit> = TelemetryScope.async {
        val last30MinsDataPoint = competitiveDataRepository.getDataPointsInWindow(
            startAt = TimeHelpers.getNow().minusMinutes(30),
            endAt = TimeHelpers.getNow(),
            compProductIds = modeOfMinRules.map { it.compProductId }.toSet()
        )
        val modeOfMinRuledCompProductIds = modeOfMinRules.groupBy { it.compProductId }
        val modeOfMinRuledUdCatalogIds = modeOfMinRules.groupBy { it.udCatalogId }
        val modeOfMinDataPoints = last30MinsDataPoint.groupBy { it.catalogId }
            .filterKeys { it in modeOfMinRuledCompProductIds }

        modeOfMinRuledUdCatalogIds.forEach { (udCatalogId, insights) ->
            val targettedCompProductIds = insights.map { it.compProductId }.toSet()
            val targettedDataPoints = modeOfMinDataPoints.filterKeys { it in targettedCompProductIds }.values.flatten()

            val minByEachUser = targettedDataPoints
                .filter { it.outOfStock != true && it.isSuccessful != false }
                .groupBy { it.competitorSpecificAttributes?.get(CompetitorSpecificAttributes.USER_ID.key) }
                .mapNotNull { it.value.minBy { data -> (data.currentPrice ?: Double.MAX_VALUE) } }

            val modeOfMinDataPoint = minByEachUser.getMode()
            val closestToMedianDataPoint by lazy { minByEachUser.getClosestToMedian() }

            insights.parallelMap { insight ->
                if (insight.minDataPoints > targettedDataPoints.size) {
                    logger.info("Not enough data points for compProductId: $udCatalogId, skipping...")
                    return@parallelMap
                }

                val finalDataPoint = modeOfMinDataPoint ?: closestToMedianDataPoint ?: targettedDataPoints.firstOrNull()

                if (finalDataPoint == null) {
                    logger.info("No data points for compProductId: ${insight.compProductId}, skipping...")
                } else {
                    logger.info("Data point found for compProductId: ${finalDataPoint.currentPrice}")
                    pricingManager.generateOnlineCompPricingSignal(
                        udCatalogId = insight.udCatalogId,
                        competitiveData = finalDataPoint,
                        city = insight.city,
                        competitor = insight.competitor,
                        mappingType = insight.mappingType,
                        compProduct = competitorCatalogRepository.getById(insight.compProductId)
                    )
                }

                generateTerritorySignal(
                    insight = insight,
                    dataPoints = minByEachUser
                )
            }
        }
    }
}

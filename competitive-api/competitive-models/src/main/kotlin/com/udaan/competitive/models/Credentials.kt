package com.udaan.competitive.models

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import java.time.ZonedDateTime

@JsonIgnoreProperties(ignoreUnknown = true)
data class CompetitorLoginAccount(
    val id: String,
    val competitorCityId: String,
    val accountIdentifierType: AccountIdentifierType,
    val accountIdentifier: String,
    val supportedLoginMethods: List<AccountLoginMethod>,
    val competitorSpecificLoginAttributes: CompetitorSpecificLoginAttributes? = null,
    val state: CompetitorLoginAccountState,
    val createdAt: ZonedDateTime,
    val updatedAt: ZonedDateTime
)

enum class AccountIdentifierType{
    EMAIL,
    PHONE_NUMBER,
    USERNAME
}

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "competitor")
@JsonSubTypes(
    // add subtypes as required
    // IMPORTANT: Do note that for Pharmarack, the competitor name in this class is simply "PHARMARACK",
    // whereas the actual competitor name used elsewhere (CompetitorCity) would be "PHARMARACK_{DISTRIBUTOR}"
    // This is done so as to have a single inherited class for all Pharmarack login attributes
    JsonSubTypes.Type(name = "PHARMARACK", value = PharmarackLoginAttributes::class),
    JsonSubTypes.Type(name = "METRO", value = MetroLoginAttributes::class)
)
sealed class CompetitorSpecificLoginAttributes{
    val competitor
        @JsonIgnore
        get() = this.javaClass.getAnnotation(JsonTypeName::class.java).value
}

data class MetroLoginAttributes(
    val pincode: String? = null,
    val requestId: String? = null,
    val applicationId: String? = null,
    val mobile: String? = null,
    val userId: String? = null
): CompetitorSpecificLoginAttributes()

data class EasySolLoginAttributes (
    val password: String
): CompetitorSpecificLoginAttributes()
data class PharmarackLoginAttributes (
    val password: String,
    val storeId: String = "",
    val retailerId: String = ""
): CompetitorSpecificLoginAttributes()


enum class AccountLoginMethod{
    EMAIL_PASSWORD,
    USERNAME_PASSWORD,
    PHONE_NUMBER_PASSWORD_OTP,
    PHONE_NUMBER_OTP,
    EMAIL_OTP,
    USERNAME_OTP
}

enum class CompetitorLoginAccountState{
    ACTIVE,
    INACTIVE
}

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "competitor")
@JsonSubTypes(
    JsonSubTypes.Type(name = "JUMBOTAIL", value = JTSessionAttributes::class),
    JsonSubTypes.Type(name = "METRO", value = MetroSessionAttributes::class),
    // IMPORTANT: Do note that for Pharmarack, the competitor name in this class is simply "PHARMARACK",
    // whereas the actual competitor name used in CompetitorCity would be "PHARMARACK_{DISTRIBUTOR}"
    // This is done to have a single inherited class for all Pharmarack session attributes
    JsonSubTypes.Type(name = "PHARMARACK", value = PharmarackSessionAttributes::class),
    JsonSubTypes.Type(name = "SAVEO", value = SaveoSessionAttributes::class),
    JsonSubTypes.Type(name = "SHIKHAR", value = ShikharSessionAttributes::class),
    JsonSubTypes.Type(name = "METROV2", value = MetroSessionAttributesV2::class),
    JsonSubTypes.Type(name = "HYPERPURE", value = HyperPureSessionAttributes::class)
)

@JsonIgnoreProperties(ignoreUnknown = true)
sealed class CompetitorSpecificSessionAttributes {
    val competitor
        @JsonIgnore
        get() = this.javaClass.getAnnotation(JsonTypeName::class.java).value
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class JTSessionAttributes(
    val otpId: String?,
    val customerId: String?,
    val location: GeoLoc? = null
): CompetitorSpecificSessionAttributes()

data class ShikharSessionAttributes(
    val hulId: String
) : CompetitorSpecificSessionAttributes()

data class MetroSessionAttributes(
    val phoneNo: String?,
    val storeId: String?,
    val userSegment: String
): CompetitorSpecificSessionAttributes()

@JsonIgnoreProperties(ignoreUnknown = true)
data class MetroSessionAttributesV2(
    val pincode: String,
    val state: String,
    val stateCode: String,
    val city: String,
    val mobileCookies: List<String>? = null,
    val headerCookie: String? = null
): CompetitorSpecificSessionAttributes()

data class HyperPureSessionAttributes(
    val accountId: Number,
    val outletId: Number
) : CompetitorSpecificSessionAttributes()

data class PharmarackSessionAttributes(
    val userId: String,
    val requestVerificationTokenCookie: String,
    val requestVerificationToken: String,
    val sessionId: String,
    val aspxAuthCookie: String? = null
): CompetitorSpecificSessionAttributes()

data class SaveoSessionAttributes(
    val sessionId: String
): CompetitorSpecificSessionAttributes()

@JsonIgnoreProperties(ignoreUnknown = true)
data class CompetitorLoginSession(
    val id: String,
    val accountId: String,
    val competitorCityId: String,
    val authorizationType: CompetitorLoginAuthorizationType,
    val authToken: String? = null,
    val cookies: LoginSessionCookies? = null,
    val tokenValidity: Long?,
    val competitorSpecificAttributes: CompetitorSpecificSessionAttributes? = null,
    val connectionError: ConnectionError?,
    val refreshable: Boolean,
    val refreshToken: String? = null,
    val refreshTokenUrl: String? = null,
    val refreshedAt: Long,
    val createdAt: Long,
    val createdBy: String,
    val state: CompetitorLoginSessionState,
    val proxy: Proxy? = null
)

data class LoginSessionCookies(
    val token: String? = null
)

enum class CompetitorLoginAuthorizationType{
    BEARER,
    OAUTH,
    BASIC
}

enum class CompetitorLoginSessionState{
    ACTIVE,
    INACTIVE,
    ERROR,
    LOGIN_REQUIRED,
    LOGIN_IN_PROGRESS,
    OTP_SENT,
    OTP_VERIFICATION_FAILED
}

enum class ConnectionError {
    INVALID_TOKEN,
    REFRESH_REQUIRED
}
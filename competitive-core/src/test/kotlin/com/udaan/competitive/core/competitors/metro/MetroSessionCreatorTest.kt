package com.udaan.competitive.core.competitors.metro

import com.udaan.competitive.core.dao.CompPage
import com.udaan.competitive.core.dao.CompPagesDao
import com.udaan.competitive.core.dao.OnlineCompSessionDao
import com.udaan.competitive.core.generateId
import com.udaan.competitive.core.getProdResource
import com.udaan.competitive.core.models.CrawlTriggerType
import com.udaan.competitive.models.City
import com.udaan.competitive.models.Competitor
import com.udaan.competitive.models.CompetitorLoginAuthorizationType
import com.udaan.competitive.models.CompetitorLoginSession
import com.udaan.competitive.models.CompetitorLoginSessionState
import com.udaan.competitive.models.MetroSessionAttributesV2
import java.sql.Timestamp
import kotlinx.coroutines.runBlocking
import org.junit.Ignore
import org.junit.Test

@Ignore
class MetroSessionCreatorTest {

    private val compPagesDao by lazy {
        getProdResource(CompPagesDao::class.java)
    }

    private val compSessionDao by lazy {
        getProdResource(OnlineCompSessionDao::class.java)
    }

    @Test
    fun handleJTBrawlCrawl() = runBlocking {
        val metroSessionAttributesV2 = MetroSessionAttributesV2(
            state = "Karnataka",
            pincode = "560100",
            city = "Bangalore",
            stateCode = "29"
        )
        compSessionDao.createOrUpdate(
            CompetitorLoginSession(
                id = generateId("CLS"),
                accountId = generateId("CLA"),
                competitorCityId = "METRO_BANGALORE",
                authorizationType = CompetitorLoginAuthorizationType.BEARER,
                authToken = "NjRkZTA3ZDJiM2I1YWI2ZDc2NGVjZDkyOndNVEY0ZjI0cw==",
                connectionError = null,
                refreshable = false,
                refreshedAt = System.currentTimeMillis(),
                createdAt = System.currentTimeMillis(),
                createdBy = "ayusman.c",
                state = CompetitorLoginSessionState.ACTIVE,
                competitorSpecificAttributes = metroSessionAttributesV2,
                tokenValidity = null
            )
        )
        println("DONE")
    }

    @Test
    fun handleNCCategoryCrawl() = runBlocking {
        val x = compSessionDao.getLatestActiveByAccountId("CLA44RYE8VE6FQ7CZ8CQQH00RD882")
        val y = x?.copy(competitorCityId =  "METRO_BANGALORE")
        if (y != null) {
            compSessionDao.createOrUpdate(y)
        }
        println("DONE")
    }

    @Test
    fun handleMetroCategoryCrawl() = runBlocking {
        val compSession = compSessionDao.getLatestActiveByAccountId("CLA7KF96R7RPPDYW41VL5VVX7LB66")
        val client = MetroClient(null, compSession?.authToken!!, compSession.competitorSpecificAttributes as MetroSessionAttributesV2)
        val categories = client.getCategoriesAsync().await()
        categories.forEach {
            val pageid = it.code ?: return@forEach
            val pagetitle = it.name ?: return@forEach
            compPagesDao.createOrUpdate(
                CompPage(
                    id = generateId("CP"),
                    pageType = CrawlTriggerType.CATEGORY,
                    pageId = pageid,
                    accountId = compSession.accountId,
                    competitor = Competitor.METRO,
                    city = City.BANGALORE,
                    updatedAt = Timestamp(System.currentTimeMillis()),
                    pageTitle = pagetitle
                )
            )
        }
        categories.forEach {
            println("Category: ${it.name} - ${it.code} - ${it.url}")
        }
        println("DONE")
    }

    @Test
    fun handleMetroBrandCrawl() = runBlocking {
        val compSession = compSessionDao.getLatestActiveByAccountId("CLA7KF96R7RPPDYW41VL5VVX7LB66")
        val client = MetroClient(null, compSession?.authToken!!, compSession.competitorSpecificAttributes as MetroSessionAttributesV2)
        val brands = client.getBrandsAsync().await()
        brands.forEach {
            val brandId = it.code ?: return@forEach
            val brandName = it.name ?: return@forEach
            compPagesDao.createOrUpdate(
                CompPage(
                    id = generateId("CP"),
                    pageType = CrawlTriggerType.BRAND,
                    pageId = brandId,
                    accountId = compSession.accountId,
                    competitor = Competitor.METRO,
                    city = City.BANGALORE,
                    updatedAt = Timestamp(System.currentTimeMillis()),
                    pageTitle = brandName
                )
            )
        }

        brands.forEach {
            println("Brand: ${it.name} - ${it.code} - ${it.url}")
        }
        println("DONE")
    }
}

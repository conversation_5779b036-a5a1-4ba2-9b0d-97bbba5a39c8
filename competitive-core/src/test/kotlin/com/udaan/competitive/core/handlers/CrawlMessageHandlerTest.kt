package com.udaan.competitive.core.handlers

import com.udaan.competitive.core.competitors.metro.MetroCategories
import com.udaan.competitive.core.getDevResource
import com.udaan.competitive.core.getProdResource
import com.udaan.competitive.core.models.BrandCrawlTrigger
import com.udaan.competitive.core.models.CategoryCrawlTrigger
import com.udaan.competitive.core.offline.dao.ProductUdCatalogMappingDao
import com.udaan.competitive.models.City
import com.udaan.competitive.models.Competitor
import kotlinx.coroutines.runBlocking
import org.junit.Ignore
import org.junit.Test
import java.sql.Timestamp

@Ignore
class CrawlMessageHandlerTest {

    private val crawlMessageHandler by lazy {
        getProdResource(CrawlMessageHandler::class.java)
    }

    @Test
    fun handleJTBrawlCrawl() = runBlocking {
        val brandTrigger = BrandCrawlTrigger(
            ccJobId = "TESTJOBID",
            accountId = "",
            brandIds = setOf(""),
            city = City.BANGALORE,
            competitor = Competitor.JUMBOTAIL
        )
        crawlMessageHandler.handleCrawlTrigger(brandTrigger)
    }

    @Test
    fun handleNCCategoryCrawl() = runBlocking {
        val brandTrigger = CategoryCrawlTrigger(
            ccJobId = "CJOBV2FKYKCWVXJBC6BGZ85X7PWPCR6T",
            accountId = "562100",
            categoryIds = setOf("1"),
            city = City.BANGALORE,
            competitor = Competitor.NINJACART
        )
        crawlMessageHandler.handleCrawlTrigger(brandTrigger)
    }

    @Test
    fun handleMetroCategoryCrawl() = runBlocking {
        val categoryCrawlTrigger = CategoryCrawlTrigger(
            ccJobId = "CJOBV303B2DC5SGJD25F6CVHB29CN5D3",
            accountId = "CLA2K1P1WDHBNDT943DY3XJ7YS1MX",
            categoryIds = setOf("beverages--health-drinks"),
            city = City.HYDERABAD,
            competitor = Competitor.METRO
        )
        crawlMessageHandler.handleCrawlTrigger(categoryCrawlTrigger)
    }
}

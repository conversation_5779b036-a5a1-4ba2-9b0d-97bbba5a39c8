package com.udaan.competitive.core.competitors.shikhar

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.competitive.core.competitors.BaseParser
import com.udaan.competitive.core.competitors.PackagedCompetitorResponse
import com.udaan.competitive.core.dao.CompetitiveDataRepository
import com.udaan.competitive.core.dao.CompetitorCatalogRepository
import com.udaan.competitive.core.models.OnlinePriceCapturePayload
import com.udaan.competitive.core.models.ProcessorQueues
import com.udaan.competitive.core.providers.EventEmitterProvider
import com.udaan.competitive.core.providers.Message
import com.udaan.competitive.models.City
import com.udaan.competitive.models.Competitor
import kotlinx.coroutines.Dispatchers
import org.slf4j.Logger

@Singleton
class ShikharParser @Inject constructor(
    private val competitorCatalogRepository: CompetitorCatalogRepository,
    private val competitiveDataRepository: CompetitiveDataRepository,
    private val eventEmitterProvider: EventEmitterProvider
) : BaseParser(
    competitorCatalogRepository,
    Competitor.SHIKHAR
) {

    override val logger: Logger by logger()

    override suspend fun parseAndRecord(city: City, jobrunId: String, packagedCompetitorResponse: PackagedCompetitorResponse) {
        when (packagedCompetitorResponse) {
            is PackagedShikharCategoryPageResponse -> handleCategoryResponse(
                city = city,
                jobrunId = jobrunId,
                response = packagedCompetitorResponse.response
            )

            else -> {
                logger.error("Unknown response type: ${packagedCompetitorResponse.javaClass.simpleName}")
            }
        }
    }

    private suspend fun handleCategoryResponse(city: City, jobrunId: String, response: ShikharCategoryPageResponse) {
        response.productgroup
            .flatMap { it.products }
            .parallelMap(10, Dispatchers.IO) { productDetail ->
                val catalog = findOrUpsertCatalogEntry(productDetail.toCompetitorCatalog(city))

                val dataPoint = productDetail.toCompetitiveData(
                    catalogId = catalog.id,
                    city = city,
                    jobRunId = jobrunId,
                    trackingMap = emptyMap()
                )
                if (dataPoint == null) {
                    logger.warn("No price found for ${productDetail.itemvarient}")
                    return@parallelMap
                }

                val compData = competitiveDataRepository.createOrUpdate(dataPoint)
                eventEmitterProvider.sendMessages(
                    queue = ProcessorQueues.OnlinePriceCaptureQueue,
                    messages = listOf(
                        Message(
                            id = compData.catalogId,
                            payload = OnlinePriceCapturePayload(
                                id = compData.id,
                                compProductId = compData.catalogId,
                                capturedAt = System.currentTimeMillis(),
                                competitorCityId = "${Competitor.METRO.name}_${city.name}",
                                parentJobRunId = jobrunId
                            )
                        )
                    )
                )
            }
    }

}
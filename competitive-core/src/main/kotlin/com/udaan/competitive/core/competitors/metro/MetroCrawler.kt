package com.udaan.competitive.core.competitors.metro

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.competitive.core.competitors.BaseCrawler
import com.udaan.competitive.core.dao.OnlineCompSessionDao
import com.udaan.competitive.core.manager.CrawlJobManager
import com.udaan.competitive.core.models.ParsePayload
import com.udaan.competitive.core.models.ProcessorQueues
import com.udaan.competitive.core.providers.EventEmitterProvider
import com.udaan.competitive.core.providers.Message
import com.udaan.competitive.models.City
import com.udaan.competitive.models.Competitor
import com.udaan.competitive.models.MetroSessionAttributesV2
import com.udaan.competitive.models.Proxy
import kotlinx.coroutines.delay

@Singleton
class MetroCrawler @Inject constructor(
    private val eventEmitterProvider: EventEmitterProvider,
    private val crawlJobManager: CrawlJobManager,
    private val onlineCompSessionDao: OnlineCompSessionDao
) : BaseCrawler(Competitor.METRO, crawlJobManager) {

    companion object {
        private val log by logger()
    }

    override suspend fun crawlCategory(
        cJobID: String,
        categoryId: String,
        accountId: String,
        city: City,
        proxy: Proxy?
    ) {
        val account = onlineCompSessionDao.getLatestActiveByAccountId(accountId)
        val authToken = account?.authToken ?: throw IllegalStateException("Account not found")
        val metroSessionAttributesV2 = account.competitorSpecificAttributes as? MetroSessionAttributesV2
            ?: throw IllegalStateException("Metro session not found for $accountId")
        val metroClient = MetroClient(
            proxy = proxy,
            accessToken = authToken,
            sessionAttributes = metroSessionAttributesV2
        )
        var nextPageId: String? = "0"
        var hasNext = true
        while (hasNext) {

            val categoryResponse = metroClient.getItemsForCategory(categoryId = categoryId, pageID = nextPageId).await()
            val items = categoryResponse?.items.orEmpty()
            val availabilityMap = metroClient.fetchItemAvailability(items, metroSessionAttributesV2.pincode.toInt())
            val availableItems = items.filter { availabilityMap.await()[it.uid]?.is_active == true }
            val ladderPricingResponse =
                metroClient.getLadderPricingBulk(availableItems, pincode = metroSessionAttributesV2.pincode.toInt()).await()

            eventEmitterProvider.sendMessages(
                ProcessorQueues.ParserQueue,
                listOf(
                    Message(
                        id = null,
                        payload = ParsePayload(
                            competitor = Competitor.METRO,
                            city = city,
                            jobRunId = cJobID,
                            response = MetroPackagedResponse(
                                items = items,
                                availabilityMap = availabilityMap.await(),
                                ladderPricingResponse = ladderPricingResponse
                            )
                        )
                    )
                )
            )
            nextPageId = categoryResponse?.page?.nextId
            hasNext = categoryResponse?.page?.hasNext ?: false
            delay(1000)
        }
    }

    override suspend fun crawlBrand(
        cJobID: String,
        brandId: String,
        accountId: String,
        city: City,
        proxy: Proxy?
    ) {
        // todo: move to single client init
        val account = onlineCompSessionDao.getLatestActiveByAccountId(accountId)
        val authToken = account?.authToken ?: throw IllegalStateException("Account not found")
        val metroSessionAttributesV2 = account.competitorSpecificAttributes as? MetroSessionAttributesV2
            ?: throw IllegalStateException("Metro session not found for $accountId")
        val metroClient = MetroClient(
            proxy = proxy,
            accessToken = authToken,
            sessionAttributes = metroSessionAttributesV2
        )
        log.info("Crawling brand: $brandId for account: $accountId in city: ${city.name}")
        var nextPageId = "*"
        var hasNext = true
        while (hasNext) {
            val brandResponse = metroClient.getBrandProducts(brandId = brandId, pageID = nextPageId).await()
            if (brandResponse == null) {
                log.error("Failed to get brand response for brand: $brandId")
                break
            }
            log.info("Brand response size: ${brandResponse.items?.size ?: 0} for brand: $brandId")
            val items = brandResponse.items.orEmpty()
            val availabilityMap = metroClient.fetchItemAvailability(items, metroSessionAttributesV2.pincode.toInt())
            val availableItems = items.filter { availabilityMap.await()[it.uid]?.is_active == true }
            val ladderPricingResponse =
                metroClient.getLadderPricingBulk(availableItems, pincode = metroSessionAttributesV2.pincode.toInt())
                    .await()

            eventEmitterProvider.sendMessages(
                queue = ProcessorQueues.ParserQueue,
                messages = listOf(
                    Message(
                        id = null,
                        payload = ParsePayload(
                            competitor = Competitor.METRO,
                            city = city,
                            jobRunId = cJobID,
                            response = MetroPackagedResponse(
                                items = items,
                                availabilityMap = availabilityMap.await(),
                                ladderPricingResponse = ladderPricingResponse
                            )
                        )
                    )
                )
            )

            if (brandResponse.page?.nextId == null) {
                log.info("Next page id is null, breaking out of loop for brand: $brandId")
                break
            } else {
                nextPageId = brandResponse.page.nextId
            }
            hasNext = brandResponse.page.hasNext ?: false
            delay(1000)
            log.info("Processed page: $nextPageId, hasNext: $hasNext for brand: $brandId")
        }
    }
}

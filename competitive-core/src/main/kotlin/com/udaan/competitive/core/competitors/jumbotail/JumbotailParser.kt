package com.udaan.competitive.core.competitors.jumbotail

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.competitive.core.competitors.BaseParser
import com.udaan.competitive.core.competitors.PackagedCompetitorResponse
import com.udaan.competitive.core.dao.CompetitiveDataRepository
import com.udaan.competitive.core.dao.CompetitorCatalogRepository
import com.udaan.competitive.core.dao.CrawlJobRunDao
import com.udaan.competitive.core.models.OnlinePriceCapturePayload
import com.udaan.competitive.core.models.ProcessorQueues
import com.udaan.competitive.core.providers.EventEmitterProvider
import com.udaan.competitive.core.providers.Message
import com.udaan.competitive.models.City
import com.udaan.competitive.models.Competitor
import com.udaan.instrumentation.Telemetry
import kotlinx.coroutines.Dispatchers
import org.slf4j.Logger

@Singleton
class JumbotailParser @Inject constructor(
    private val competitiveDataRepository: CompetitiveDataRepository,
    private val competitorCatalogRepository: CompetitorCatalogRepository,
    private val jobRunDao: CrawlJobRunDao,
    private val eventEmitterProvider: EventEmitterProvider
) : BaseParser(
    competitorCatalogRepository,
    Competitor.JUMBOTAIL
) {

    override val logger: Logger by logger()

    override suspend fun parseAndRecord(
        city: City,
        jobrunId: String,
        packagedCompetitorResponse: PackagedCompetitorResponse
    ) {
        when (packagedCompetitorResponse) {
            is PackagedJTProductResultsPage -> handleProductResultsPage(
                jobrunId = jobrunId,
                city = city,
                response = packagedCompetitorResponse.jtProductResultsPage
            )

            else -> {
                logger.error("Unknown response type: ${packagedCompetitorResponse.javaClass.simpleName}")
            }
        }
    }

    private suspend fun handleProductResultsPage(city: City, response: JTProductResultsPage, jobrunId: String) {
        val customerId = try {
            jobRunDao.getAccountIdForParentJob(jobrunId)
        } catch (e: Exception) {
            logger.error("Failed to get account id for jobrunId: $jobrunId", e)
            Telemetry.trackException(e, mapOf("jobrunId" to jobrunId))
            null
        }
        response.data.productGroupResponseList.forEach {
            it.productsDetailsList.parallelMap(10, Dispatchers.IO) { productDetail ->
                try {
                    val catalog =
                        productDetail.toCompetitorProduct(city)?.let { cproduct -> findOrUpsertCatalogEntry(cproduct) }
                            ?: throw IllegalStateException("Failed to convert product details to competitor product")

                    val dataPoint = productDetail.toCompetitiveData(
                        cJobID = jobrunId,
                        competitorCatalog = catalog,
                        customerId = customerId ?: "jumbotail" //todo: remove this hardcoding
                    ) ?: throw IllegalStateException("Failed to convert to data point ${catalog.id}")

                    logger.info("Captured ${productDetail.title} ${dataPoint.id} for ${dataPoint.catalogId} in ${dataPoint.competitorCityId}")
                    val competitiveData = competitiveDataRepository.createOrUpdate(dataPoint)
                    eventEmitterProvider.sendMessages(
                        queue = ProcessorQueues.OnlinePriceCaptureQueue,
                        messages = listOf(
                            Message(
                                id = catalog.id,
                                payload = OnlinePriceCapturePayload(
                                    id = competitiveData.id,
                                    compProductId = catalog.id,
                                    capturedAt = competitiveData.createdAt,
                                    competitorCityId = dataPoint.competitorCityId,
                                    parentJobRunId = jobrunId
                                )
                            )
                        )
                    )
                } catch (e: Exception) {
                    logger.error("Failed to process product details: ${productDetail.title}", e)
                }
            }
        }
    }

}


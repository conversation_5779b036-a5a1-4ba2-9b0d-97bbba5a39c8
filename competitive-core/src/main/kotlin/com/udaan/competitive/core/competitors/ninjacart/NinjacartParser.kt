package com.udaan.competitive.core.competitors.ninjacart

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.competitive.core.competitors.BaseParser
import com.udaan.competitive.core.competitors.PackagedCompetitorResponse
import com.udaan.competitive.core.dao.CompetitiveDataRepository
import com.udaan.competitive.core.dao.CompetitorCatalogRepository
import com.udaan.competitive.core.models.OnlinePriceCapturePayload
import com.udaan.competitive.core.models.ProcessorQueues
import com.udaan.competitive.core.providers.EventEmitterProvider
import com.udaan.competitive.core.providers.Message
import com.udaan.competitive.core.utils.TelemetryHelper
import com.udaan.competitive.models.City
import com.udaan.competitive.models.Competitor
import org.slf4j.Logger

@Singleton
class NinjacartParser @Inject constructor(
    private val competitorCatalogRepository: CompetitorCatalogRepository,
    private val competitiveDataRepository: CompetitiveDataRepository,
    private val eventEmitterProvider: EventEmitterProvider
): BaseParser(competitorCatalogRepository, Competitor.NINJACART) {

    override val logger: Logger by logger()

    override suspend fun parseAndRecord(
        city: City,
        jobrunId: String,
        packagedCompetitorResponse: PackagedCompetitorResponse
    ) {
        if (packagedCompetitorResponse !is PackagedNinjacartCategoryResponse) {
            logger.error("Unknown response type: ${packagedCompetitorResponse.javaClass.simpleName}")
            return
        }

        val response = packagedCompetitorResponse.ninjacartCategoryResponse
        if (!response.success) {
            logger.error("Ninjacart API response unsuccessful: ${response.errorMessage}")
            return
        }

        val products = response.data
        logger.info("Processing ${products.size} Ninjacart products")

        products.forEach { product ->
            try {
                logger.info("Processing product: ${product.sku.id} - ${product.sku.name}")
                val extractedProduct = product.toCompetitorCatalog(city)
                if (extractedProduct == null) {
                    logger.error("Unable to convert product to competitor product: ${product.sku.id} - ${product.sku.name}")
                    return@forEach
                }
                val competitorProduct = findOrUpsertCatalogEntry(extractedProduct)
                val extractedData = product.toCompetitiveData(
                    jobRunId = jobrunId,
                    city = city,
                    catalogId = competitorProduct.id
                )
                if (extractedData == null) {
                    logger.error("Unable to convert product to competitive data: ${product.sku.id} - ${product.sku.name}")
                    return@forEach
                }

                logger.info("Recording competitive data for Ninjacart product: ${product.sku.id} - ${product.sku.name}")
                val competitiveData = competitiveDataRepository.createOrUpdate(extractedData)
                eventEmitterProvider.sendMessages(
                    queue = ProcessorQueues.OnlinePriceCaptureQueue,
                    messages = listOf(
                        Message(
                            id = competitiveData.catalogId,
                            payload = OnlinePriceCapturePayload(
                                id = competitiveData.id,
                                compProductId = competitiveData.catalogId,
                                capturedAt = System.currentTimeMillis(),
                                competitorCityId = competitiveData.competitorCityId,
                                parentJobRunId = jobrunId
                            )
                        )
                    )
                )
            } catch (e: Exception) {
                TelemetryHelper.trackException(
                    e = e,
                    labels = mapOf(
                        "jobId" to jobrunId,
                        "productName" to product.sku.name,
                        "city" to city.name
                    )
                )
                logger.error("Error processing Ninjacart product ${product.sku.id}: ${e.message}", e)
            }
        }
    }
}
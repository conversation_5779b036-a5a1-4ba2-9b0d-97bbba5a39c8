package com.udaan.competitive.core.competitors.metro

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.PropertyNamingStrategy
import com.fasterxml.jackson.module.kotlin.KotlinModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.udaan.competitive.core.utils.HttpClientV2
import com.udaan.competitive.core.utils.HttpClientV2Helpers.makeRequest
import com.udaan.competitive.core.utils.RequestMethod
import com.udaan.competitive.models.MetroCategory
import com.udaan.competitive.models.MetroSessionAttributesV2
import com.udaan.competitive.models.Proxy
import com.udaan.instrumentation.TelemetryScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.awaitAll
import okhttp3.HttpUrl
import org.jsoup.Jsoup
import org.slf4j.LoggerFactory
import java.net.URL

class MetroClient(
    private val proxy: Proxy?,
    val accessToken: String,
    private val sessionAttributes: MetroSessionAttributesV2
) {

    companion object {
        private const val BASE_URL = "www.metrorrl.com"
        private val objectMapper = ObjectMapper()
            .registerModule(KotlinModule())
            .apply { propertyNamingStrategy = PropertyNamingStrategy.SNAKE_CASE }
        private val jsonObjectMapper = jacksonObjectMapper().apply {
            propertyNamingStrategy = PropertyNamingStrategy.SNAKE_CASE
        }
        private val logger = LoggerFactory.getLogger(MetroClient::class.java)
        private const val LADDER_PRICING_CHUNK_SIZE = 8
    }

    private val LOCATION: String = ObjectMapper().writeValueAsString(
        mapOf(
            "country" to "INDIA",
            "country_iso_code" to "IN",
            "pincode" to sessionAttributes.pincode,
            "state_code" to sessionAttributes.stateCode,
            "city" to sessionAttributes.city,
            "state" to sessionAttributes.state
        )
    )

    private fun getAuthorisedHeader(includeCookie: Boolean = true) : Map<String, String> {
        return MetroHttpUtils.buildHeaders(
            accessToken = accessToken,
            includeCookie = includeCookie,
            cookie = sessionAttributes.headerCookie,
            location = LOCATION
        )
    }

    // OkHttpClient instance for making HTTP requests
    private val client = HttpClientV2.buildClient(
        maxRetry = 2,
        authorization = null,
        interceptors = listOf(MetroRequestSignerInterceptor)
    )

    private fun getHomeHTML(): Deferred<String> {
        return TelemetryScope.async(Dispatchers.IO) {
            val homeUrl = "https://$BASE_URL"
            logger.info("Fetching Metro homepage from $homeUrl")
            Jsoup.connect(homeUrl)
                .headers(getAuthorisedHeader(includeCookie = false))
                .ignoreContentType(true)
                .timeout(10_000)
                .get()
                .html()
        }
    }

    private suspend fun getCategories(): List<MetroCategory> {
        val response = getHomeHTML().await()
        val rawJson = MetroHttpUtils.extractRawJsonFromHtml(response)
        val categories = extractCategoriesFromJson(rawJson)
        logger.info("Fetched ${categories.size} categories from the Script Json")
        return categories
    }

    private suspend fun getBrands(): List<MetroBrand> {
        val response = getHomeHTML().await()
        val rawJson = MetroHttpUtils.extractRawJsonFromHtml(response)
        val brands = extractBrandsFromJson(rawJson)
        logger.info("Fetched ${brands.size} brands from the Script Json")
        return brands
    }

    // Fetches categories dynamically from the baseUrl by parsing the homepage HTML.
    fun getCategoriesAsync(): Deferred<List<MetroCategory>> {
        return TelemetryScope.async {
            getCategories()
        }
    }

    // Fetches brands dynamically from the baseUrl by parsing the homepage HTML.
    fun getBrandsAsync(): Deferred<List<MetroBrand>> {
        return TelemetryScope.async {
            getBrands()
        }
    }

    private fun extractCategoriesFromJson(cleanedJson: String): List<MetroCategory> {
        val metroHomeResponse: MetroHomeResponse = jsonObjectMapper.readValue(cleanedJson)
        return metroHomeResponse.appmeta?.availablePages?.home?.sections
            ?.firstOrNull { it.label.equals("Shop By Categories", ignoreCase = true) }
            ?.blocks
            ?.mapNotNull { block ->
                val title = block.props?.blockTitle?.value
                val url = block.props?.blockLink?.value
                if (!url.isNullOrBlank() && !title.isNullOrBlank()) {
                    runCatching {
                        val uri = URL(url)
                        val categoryCode = uri.path.split("/").lastOrNull { it.isNotBlank() }?.split("?")?.firstOrNull()
                        MetroCategory(code = categoryCode, name = title, url = uri.toString().substringBefore("?"))
                    }.getOrElse {
                        logger.warn("Failed to process category URL: $url", it)
                        null
                    }
                } else null
            } ?: emptyList()
    }


    private fun extractBrandsFromJson(cleanedJson: String): List<MetroBrand> {
        val metroHomeResponse: MetroHomeResponse = jsonObjectMapper.readValue(cleanedJson)
        return metroHomeResponse.appmeta?.availablePages?.home?.sections
            ?.firstOrNull { it.label.equals("Shop by Brands", ignoreCase = true) }
            ?.blocks
            ?.mapNotNull { block ->
                val brandValue = block.props?.brand?.value
                val name = brandValue?.display
                val code = brandValue?.id
                val url = "https://${BASE_URL}/products/?brand=$code&source=suggestion"
                if (!name.isNullOrBlank() && !code.isNullOrBlank()) {
                    MetroBrand(code = code, name = name, url = url)
                } else null
            } ?: emptyList()
    }

    /**
     * Fetches all the items for a given category ID using cursor-based pagination.
     * @param categoryId the ID of the category for which items are to be fetched.
     * @return a list of MetroItem objects.
     */
    internal fun getItemsForCategory(categoryId: String, pageID: String?, pageSize: Int = 36): Deferred<MetroItemsResponse?> {
        return TelemetryScope.async(Dispatchers.IO) {
            val urlForItems = "/ext/catalogueDiscovery/api/search/v1.0/collections/$categoryId/items/"
            logger.info("Fetching items for category $categoryId from $urlForItems")
            val authorisedHeadersMap = getAuthorisedHeader()
            val params: Map<String, String> = buildMap {
                put("f", "is_available:true:::search:true:::source:plp")
                put("page_id", pageID ?: "*")
                put("page_size", pageSize.toString())
                if (pageID != null) put("filter", false.toString())
            }
            val urlWithParams = buildUrlWithParams(urlForItems, params)
            val response =
                client.makeRequest(urlWithParams, RequestMethod.GET, headers = authorisedHeadersMap, proxy = proxy)
            if (response != null) {
                objectMapper.readValue<MetroItemsResponse>(response)
            } else {
                null
            }
        }
    }

    internal fun getBrandProducts(
        brandId: String,
        pageID: String,
        pageSize: Int = 20
    ): Deferred<MetroItemsResponse?> {
        return TelemetryScope.async(Dispatchers.IO) {
            val urlForItems = "/ext/catalogueDiscovery/api/search/v1.0/products/"
            logger.info("Fetching items from products page with pageID: $pageID")
            val authorisedHeadersMap = getAuthorisedHeader()
            val params: Map<String, String> = buildMap {
                put("f", "brand:${brandId}:::search:true:::source:suggestion")
                put("page_id", pageID)
                put("page_size", pageSize.toString())
            }
            val urlWithParams = buildUrlWithParams(urlForItems, params)
            val response =
                client.makeRequest(
                    urlWithParams,
                    RequestMethod.GET,
                    headers = authorisedHeadersMap,
                    proxy = proxy,
                    klass = MetroItemsResponse::class.java
                )
            response?.body
        }
    }

    // Fetch item availability for given items.
    internal fun fetchItemAvailability(items: List<MetroItem>, pincode: Int): Deferred<Map<Long, MetroItemAvailability>> {
        return TelemetryScope.async {
            val availabilityUrl = "/ext/infibeam/application/api/v1.0/min-max-mul"
            val requestPayload = UidCheckRequest(
                items = items.map { it.uid.toInt() },
                pincode = pincode.toString()
            )
            val requestBodyString = objectMapper.writeValueAsString(requestPayload)
            val availabilityUrlBu = buildUrlWithParams(availabilityUrl, emptyMap())
            val responseBody = client.makeRequest(
                availabilityUrlBu,
                RequestMethod.POST,
                headers = getAuthorisedHeader(),
                body = requestBodyString,
                proxy = proxy
            )

            try {
                if (responseBody != null) {
                    objectMapper.readValue<Map<String, MetroItemAvailability>>(responseBody)
                        .mapKeys { it.key.toLong() }
                } else {
                    emptyMap()
                }
            } catch (e: Exception) {
                logger.error("Failed to parse availability JSON", e)
                emptyMap()
            }
        }
    }

    private fun getLadderPricing(
        items: List<MetroItem>,
        pincode: Int
    ): Deferred<LadderPricingResponse?> {
        return TelemetryScope.async(Dispatchers.IO) {
            val ladderPricingUrl = "/ext/infibeam/application/api/v1.0/check-ladders/"
            val requestItems = items.map { l -> l.toLadderRequestItem() }
            if (requestItems.isEmpty()) {
                logger.error("No items to fetch ladder pricing for.")
                return@async null
            }

            val requestPayload = LadderPricingRequest(
                items = requestItems,
                pincode = pincode.toString(),
                zoneId = ""
            )

            val responseBody = client.makeRequest(
                buildUrlWithParams(ladderPricingUrl, emptyMap()),
                RequestMethod.POST,
                headers = getAuthorisedHeader(),
                body = objectMapper.writeValueAsString(requestPayload),
                proxy = proxy
            )
            logger.info("Fetched ladder pricing for items: ${items.map { it.uid }}")
            return@async responseBody?.let { objectMapper.readValue<LadderPricingResponse>(responseBody) }
        }
    }

    // Fetch ladder pricing for given items.
    internal fun getLadderPricingBulk(
        availableItems: List<MetroItem>,
        pincode: Int
    ): Deferred<LadderPricingResponse> {
        return TelemetryScope.async {
            val ladderPricingMap = mutableMapOf<String, List<MetroLadder>>()
            availableItems.chunked(LADDER_PRICING_CHUNK_SIZE).map { chunk ->
                TelemetryScope.async {
                    getLadderPricing(chunk, pincode).await()?.availableLadders.orEmpty()
                }
            }
                .awaitAll()
                .forEach { laddersMap ->
                    laddersMap.forEach { (itemCode, ladders) ->
                        ladderPricingMap[itemCode] = ladders
                    }
                }
            return@async LadderPricingResponse(ladderPricingMap)
        }
    }

    // To build URL with query parameters for HTTP requests
    private fun buildUrlWithParams(path: String, params: Map<String, String>): URL {
        val urlbuilder = HttpUrl.Builder()
            .scheme("https")
            .host(BASE_URL)
            .encodedPath(path)
        for (i in params) {
            urlbuilder.addQueryParameter(i.key, i.value)
        }
        return urlbuilder.build().toUrl()
    }
}

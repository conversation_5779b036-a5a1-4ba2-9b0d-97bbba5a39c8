package com.udaan.competitive.core.dao

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.competitive.models.*
import com.udaan.cosmosdb.utils.CosmosDbDao
import com.udaan.cosmosdb.utils.makeSqlQuerySpec
import java.time.ZonedDateTime
import kotlinx.coroutines.flow.toList

@Singleton
class CompetitiveDataRepository @Inject constructor(private val objectMapper: ObjectMapper) {

    companion object {
        private val documentDbDao by lazy {
            CosmosDbDao(
                configKey = "fp-pricing",
                databaseName = dbname,
                containerName = competitive_data_table
            )
        }
    }

    private fun CompetitiveData.toDocument() = objectMapper.convertValue(this, ObjectNode::class.java)
    private fun ObjectNode.toCompetitiveData() = objectMapper.convertValue(this, CompetitiveData::class.java)

    suspend fun createOrUpdate(competitiveData: CompetitiveData): CompetitiveData {
        return documentDbDao.createOrUpdateItem(competitiveData.toDocument()).toCompetitiveData()
    }


    suspend fun getDataPointsInWindow(startAt: ZonedDateTime, endAt: ZonedDateTime, compProductIds: Set<String> = emptySet()): List<CompetitiveData> {
        return documentDbDao.queryItems(
            "getDataPointsInWindow", makeSqlQuerySpec(
                """
                    select * from c 
                    where c.createdAt >= @startTime
                    and c.createdAt <= @endTime
                """
                    + if (compProductIds.isNotEmpty()) {
                        " and c.catalogId in (@compProductIds)"
                    } else {
                        ""
                    }
                    .trimIndent(),
                "@startTime" to startAt.toInstant().toEpochMilli(),
                "@endTime" to endAt.toInstant().toEpochMilli(),
                "@compProductIds" to compProductIds.joinToString(",") { "'$it'" }
            )
        ).toList().map { it.toCompetitiveData() }
    }

    suspend fun getDataPointsInWindow(startAt: ZonedDateTime, endAt: ZonedDateTime, compProductId: String): List<CompetitiveData> {
        return documentDbDao.queryItems(
            "getDataPointsInWindow", makeSqlQuerySpec(
                """
                    select * from c 
                    where c.createdAt >= @startTime
                    and c.createdAt <= @endTime
                    and c.catalogId = @compProductId
                """.trimIndent(),
                "@startTime" to startAt.toInstant().toEpochMilli(),
                "@endTime" to endAt.toInstant().toEpochMilli(),
                "@compProductId" to compProductId
            )
        ).toList().map { it.toCompetitiveData() }
    }
}

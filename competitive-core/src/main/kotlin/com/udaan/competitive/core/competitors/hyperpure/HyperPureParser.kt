package com.udaan.competitive.core.competitors.hyperpure

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.competitive.core.competitors.BaseParser
import com.udaan.competitive.core.competitors.PackagedCompetitorResponse
import com.udaan.competitive.core.dao.CompetitiveDataRepository
import com.udaan.competitive.core.dao.CompetitorCatalogRepository
import com.udaan.competitive.core.models.OnlinePriceCapturePayload
import com.udaan.competitive.core.models.ProcessorQueues
import com.udaan.competitive.core.providers.EventEmitterProvider
import com.udaan.competitive.core.providers.Message
import com.udaan.competitive.core.utils.TelemetryHelper
import com.udaan.competitive.models.City
import com.udaan.competitive.models.Competitor
import org.slf4j.Logger

@Singleton
class HyperPureParser @Inject constructor(
    private val competitorCatalogRepository: CompetitorCatalogRepository,
    private val competitiveDataRepository: CompetitiveDataRepository,
    private val eventEmitterProvider: EventEmitterProvider
): BaseParser(competitorCatalogRepository, Competitor.HYPERPURE) {

    override val logger: Logger by logger()

    override suspend fun parseAndRecord(
        city: City,
        jobrunId: String,
        packagedCompetitorResponse: PackagedCompetitorResponse
    ) {
        if (packagedCompetitorResponse !is PackagedHyperPureCategoryResponse) {
            logger.error("Unknown response type: ${packagedCompetitorResponse.javaClass.simpleName}")
            return
        }

        val products = packagedCompetitorResponse.hyperPureCategoryResponse.response.Products
        logger.info("Processing ${products.size} HyperPure products")

        products.forEach { product ->
            try {
                logger.info("Processing product: ${product.Id} - ${product.Name}")
                val extractedProduct = product.toCompetitorCatalog(city)
                if (extractedProduct == null) {
                    logger.error("Unable to convert product to competitor product: ${product.Id} - ${product.Name}")
                    return@forEach
                }
                val competitorProduct = findOrUpsertCatalogEntry(extractedProduct)
                val extractedData = product.toCompetitiveData(
                    jobRunId = jobrunId,
                    city = city,
                    catalogId = competitorProduct.id,
                    trackingMap = mapOf(
                        "warehouseCode" to product.WarehouseCode,
                        "priceType" to product.PriceType,
                        "vertical" to (product.Vertical ?: ""),
                        "categoryName" to product.CategoryName
                    )
                )
                if (extractedData == null) {
                    logger.error("Unable to convert product to competitive data: ${product.Id} - ${product.Name}")
                    return@forEach
                }

                logger.info("Recording competitive data for HyperPure product: ${product.Id} - ${product.Name}")
                val competitiveData = competitiveDataRepository.createOrUpdate(extractedData)
                eventEmitterProvider.sendMessages(
                    queue = ProcessorQueues.OnlinePriceCaptureQueue,
                    messages = listOf(
                        Message(
                            id = competitiveData.catalogId,
                            payload = OnlinePriceCapturePayload(
                                id = competitiveData.id,
                                compProductId = competitiveData.catalogId,
                                capturedAt = System.currentTimeMillis(),
                                competitorCityId = competitiveData.competitorCityId,
                                parentJobRunId = jobrunId
                            )
                        )
                    )
                )
            } catch (e: Exception) {
                TelemetryHelper.trackException(
                    e = e,
                    labels = mapOf(
                        "jobId" to jobrunId,
                        "productName" to product.Name,
                        "city" to city.name
                    )
                )
                logger.error("Error processing HyperPure product ${product.Id}: ${e.message}", e)
            }
        }
    }
}
package com.udaan.competitive.core.competitors.metro

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.udaan.competitive.core.competitors.PackagedCompetitorResponse

// INITIAL_RESPONSE from Metro Home Page HTML
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
data class MetroHomeResponse(
    val appmeta: AppMeta?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class AppMeta(
    val availablePages: AvailablePages?,
    val appconfig: AppConfig?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class AppConfig(
    val application: AppToken
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class AppToken(
    @JsonProperty("_id") val id: String?,
    val token: String?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class AvailablePages(
    val home: Home?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Home(
    val sections: List<Section>?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Section(
    val name: String?,
    val label: String?,
    val blocks: List<Block>?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Block(
    val props: BlockProps?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class BlockProps(
    val blockTitle: BlockTitle?,
    val blockLink: BlockLink?,
    val brand: BrandProp?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class BrandValue(
    val display: String?,
    val id: String?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class BrandProp(
    val value: BrandValue?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class BlockTitle(
    val value: String?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class BlockLink(
    val value: String?
)


// Metro Items from Category Page

@JsonIgnoreProperties(ignoreUnknown = true)
internal data class MetroItemsResponse(
    val filters: List<Any>? = null,
    val items: List<MetroItem>? = null,
    val page: PageInfo? = null
)

internal sealed class Filter {
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class KeyFilter(
        val display: String?,
        val name: String?,
        val kind: String?,
        val logo: String?
    ) : Filter()

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class ValueFilter(
        val display: String?,
        val count: Int?,
        val isSelected: Boolean?,
        val value: String?,
        val logo: String?
    ) : Filter()

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class UnknownFilter(
        val rawData: Map<String, Any?>
    ) : Filter()
}

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
internal data class PageInfo(
    val type: String?,
    val nextId: String?,
    val hasPrevious: Boolean?,
    val hasNext: Boolean?,
    val itemTotal: Int?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Price(
    val marked: MarkedPrice?,
    val effective: EffectivePrice?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class MarkedPrice(
    val min: Double?,
    val max: Double?,
    val currencyCode: String?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class EffectivePrice(
    val min: Double?,
    val max: Double?,
    val currencyCode: String?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Brand(
    val name: String?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Category(
    val uid: Int?,
    val name: String?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Attributes(
    @JsonProperty("article-type") val articleType: String?,
    @JsonProperty("pack-size") val packWeight: String?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Media(
    val url: String?
)

// UID Availability Check Models
internal data class UidCheckRequest(
    val items: List<Int>,
    val pincode: String
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class MetroItemAvailability(
    val item_name: String,
    val item_id: String,
    val min_OQ: Int,
    val max_OQ: Int,
    val multiplier: Int,
    val is_active: Boolean
)


@JsonIgnoreProperties(ignoreUnknown = true)
data class MetroItem(
    val uid: Long,
    val itemCode: String?,
    val attributes: Attributes?,
    val name: String,
    val brand: Brand?,
    val slug: String?,
    val price: Price?,
    val medias: List<Media>?,
    val categoryID: String?,
    val categories: List<Category>?
) {
    val articleType: String?
        get() = attributes?.articleType

    val imageUrl: String?
        get() = medias?.firstOrNull()?.url

    private fun extractPackWeight(): Pair<Double?, String>? {
        val packWeight = attributes?.packWeight ?: return null
        val parts = packWeight.split(" ")
        if (parts.size != 2) return null
        return parts[0].toDoubleOrNull() to parts[1]
    }

    val packWeight: Double?
        get() = extractPackWeight()?.first

    val packWeightUnit: String?
        get() = extractPackWeight()?.second

    fun toLadderRequestItem(): LadderRequestItem {
        return LadderRequestItem(
            uid = uid,
            item_code = itemCode,
            article_type = attributes?.articleType,
            marked_price = MinMaxPrice(
                min = price?.marked?.min,
                max = price?.marked?.max
            ),
            effective_price = MinMaxPrice(
                min = price?.effective?.min,
                max = price?.effective?.max
            )
        )
    }

}

// Represents the request body when fetching ladder prices.
internal data class LadderPricingRequest(
    val items: List<LadderRequestItem>,
    val pincode: String,
    val zoneId: String = ""
)

data class LadderRequestItem(
    val uid: Long?,
    val item_code: String?,
    val article_type: String?,
    val marked_price: MinMaxPrice?,
    val effective_price: MinMaxPrice?
)

data class MinMaxPrice(
    val min: Double?,
    val max: Double?
)

// Represents the top-level response for ladder pricing.
@JsonIgnoreProperties(ignoreUnknown = true)
data class LadderPricingResponse(
    val availableLadders: Map<String, List<MetroLadder>>?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class MetroLadder(
    val minQuantity: Int?,
    val maxQuantity: Int?,
    val type: String?,
    val endTime: String?,
    val startTime: String?,
    val price: MetroLadderPrice?,
    val margin: String?,
    val ladderPrice: Double?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class MetroLadderPrice(
    val marked: Double?,
    val effective: Double?,
    val offerPrice: String?
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class MetroOtpGenerateResponse(
    val success: Boolean,
    val requestId: String,
    val message: String,
    val mobile: String,
    val countryCode: String,
    val resendTimer: Int,
    val resendToken: String,
    val applicationId: String,
    val userId: String
)


// Represents the top-level response for user information from Metro otp verification.
@JsonIgnoreProperties(ignoreUnknown = true)
internal data class UserResponse(
    val xJmpSession: String,
    val mobileCookies: List<String>
)

// Represents the top-level response for pincode information.
@JsonIgnoreProperties(ignoreUnknown = true)
data class PincodeInfoResponse(
    val name: String,
    val displayName: String,
    val meta: Map<String, Any> = emptyMap(),
    val localities: List<Locality>
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Locality(
    val displayName: String,
    val type: String,
    val meta: Meta = Meta()
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Meta(
    val stateCode: String? = null,
    val latitude: String? = null,
    val longitude: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
data class MetroBrand(
    val code: String? = null,
    val name: String? = null,
    val url: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class MetroPackagedResponse(
    val availabilityMap: Map<Long, MetroItemAvailability>,
    val ladderPricingResponse: LadderPricingResponse,
    val items: List<MetroItem>
): PackagedCompetitorResponse()
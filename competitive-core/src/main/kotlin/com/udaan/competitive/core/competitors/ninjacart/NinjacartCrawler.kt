package com.udaan.competitive.core.competitors.ninjacart

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.competitive.core.competitors.BaseCrawler
import com.udaan.competitive.core.manager.CrawlJobManager
import com.udaan.competitive.core.models.ParsePayload
import com.udaan.competitive.core.models.ProcessorQueues
import com.udaan.competitive.core.providers.EventEmitterProvider
import com.udaan.competitive.core.providers.Message
import com.udaan.competitive.core.utils.HttpClientV2
import com.udaan.competitive.core.utils.HttpClientV2Helpers.makeRequest
import com.udaan.competitive.core.utils.RequestMethod
import com.udaan.competitive.core.utils.StringUtils.parameterize
import com.udaan.competitive.models.*
import java.net.URL

@Singleton
class NinjacartCrawler @Inject constructor(
    private val crawlJobManager: CrawlJobManager,
    private val objectMapper: ObjectMapper,
    private val eventEmitterProvider: EventEmitterProvider
): BaseCrawler(Competitor.NINJACART, crawlJobManager) {

    companion object {
        private val log by logger()

        private const val HOST = "app.ninjacart.in"
        private const val USERAGENT = "Dalvik/2.1.0 (Linux; U; Android 14; A142 Build/UP1A.231005.007)"
        private const val CURRENT_APP_VERSION = "150"
        private const val DEVICE_ID = "649deef4-071b-44ab-8f1f-2f857d149901"

        //TODO: remove and move to session configs
        private const val AUTH_TOKEN = "1m53rcvge71s28sjaens7ki9jsj7k2caofhgn7meq4m0rm2or4nb"
        private const val CUSTOMER_ID = "582498"
        private val city2IdMapping = mapOf(
            City.BANGALORE to "2"
        )
        private const val CATEGORY_ID = "1"
        private const val PAGE_SIZE = 10
    }

    fun getHeaders(authToken: String): MutableMap<String, String> {
        val headers = mutableMapOf<String, String>()
        headers["Accept-Encoding"] = "gzip"
        headers["appName"] = "Skadi"
        headers["Connection"] = "Keep-Alive"
        headers["Auth-Type"] = "token"
        headers["Host"] = HOST
        headers["Unique-Device-Id"] = DEVICE_ID
        headers["appVersion"] = CURRENT_APP_VERSION
        headers["User-Agent"] = USERAGENT
        headers["Authorization"] = "Bearer $authToken"
        return headers
    }

    fun buildCategoryURL(categoryId: String, customerId: String, offset: Int): String {
        return "http://$HOST/knowhere/skuCategory/listSku" +
                "?limit=$PAGE_SIZE&status=1&orderMode=1&distributionChannelFlow=true&skuTypeIds=1,2" +
                "&cityId={cityId}&categoryId={categoryId}&customerId={customerId}&offset={offset}"
                    .parameterize(
                        mapOf(
                            "cityId" to city2IdMapping.get(City.BANGALORE).orEmpty(),
                            "categoryId" to CATEGORY_ID,
                            "customerId" to CUSTOMER_ID,
                            "offset" to offset.toString()
                        )
                    )
    }

    private fun makeCategoryRequest(
        httpClientV2: HttpClientV2,
        proxy: Proxy?,
        categoryId: String,
        customerId: String,
        offset: Int
    ): NinjacartCategoryResponse? {
        val categoryURL = buildCategoryURL(categoryId, customerId, offset)
        // pass proxy once proxy server has capability for gzip
        val response = httpClientV2.makeRequest(
            url = URL(categoryURL),
            headers = getHeaders(AUTH_TOKEN),
            method = RequestMethod.GET,
            proxy = null
        ) ?: return null
        return objectMapper.readValue<NinjacartCategoryResponse>(response)
    }

    private fun makeAuthorization(): AccessToken {
        return AccessToken(
            userId = Pair(CompetitorSpecificAttributes.USER_ID.key, CUSTOMER_ID),
            accessToken = Pair(CompetitorSpecificAttributes.AUTH_TOKEN.key, AUTH_TOKEN),
            mobile = 100 //Adding a dummy value as this is not required for JT
        )
    }

    override suspend fun crawlCategory(cJobID: String, categoryId: String, accountId: String, city: City, proxy: Proxy?) {
        log.info("crawlByBrandNew cJobID:$cJobID on brand: $categoryId with account:$accountId using proxy: ${proxy?.id}")
        val httpClient = HttpClientV2.buildClient(authorization = makeAuthorization())
        var hasMoreProducts = true
        var offSet = 0
        while (hasMoreProducts) {
            val response = makeCategoryRequest(httpClient, proxy, categoryId, CUSTOMER_ID, offSet)
            if (response == null) {
                log.warn(
                    "Received null response from server while crawling categoryId: {} with offset: {} using customerId: {} and proxy: {}",
                    categoryId, offSet, CUSTOMER_ID, proxy?.readable()
                )
                hasMoreProducts = false
            } else {
                emitResponse(cJobID, city, response)
                // to decide if more pagination call is required
                hasMoreProducts = response.data.size == PAGE_SIZE
                if (hasMoreProducts) {
                    offSet += PAGE_SIZE
                }
            }
        }
    }

    private suspend fun emitResponse(cJobID: String, city: City, ninjacartCategoryResponse: NinjacartCategoryResponse) {
        val parsePayload = ParsePayload(
            competitor = Competitor.NINJACART,
            city = city,
            jobRunId = cJobID,
            response = PackagedNinjacartCategoryResponse(ninjacartCategoryResponse)
        )
        return eventEmitterProvider.sendMessages(
            ProcessorQueues.ParserQueue, listOf(
                Message(
                    id = null,
                    payload = parsePayload
                )
            )
        )
    }

    override suspend fun crawlBrand(
        cJobID: String,
        brandId: String,
        accountId: String,
        city: City,
        proxy: Proxy?
    ) {
        TODO("Not yet implemented")
    }

}

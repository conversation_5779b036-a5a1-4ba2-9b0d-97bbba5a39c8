package com.udaan.competitive.core.models

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.type.TypeFactory
import com.fasterxml.jackson.module.kotlin.readValue
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import java.lang.reflect.ParameterizedType
import java.lang.reflect.Type
import microsoft.sql.DateTimeOffset
import org.jdbi.v3.core.mapper.RowMapper
import org.jdbi.v3.core.statement.StatementContext
import org.joda.time.DateTime
import java.sql.ResultSet
import java.sql.Timestamp
import java.sql.Date as SqlDate
import java.time.Instant
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.reflect.KParameter
import kotlin.reflect.full.isSubtypeOf
import kotlin.reflect.full.primaryConstructor
import kotlin.reflect.jvm.isAccessible
import kotlin.reflect.jvm.javaType
import kotlin.reflect.typeOf
import org.postgresql.geometric.PGpoint

open class DefaultRowMapper<C : Any>(private val clazz: Class<C>) : RowMapper<C> {
    companion object {
        private val mapper = ObjectMapper().registerKotlinModule()
    }

    private val klass = clazz.kotlin

    override fun map(rs: ResultSet?, ctx: StatementContext?): C? {
        return rs?.let {
            val constructor = klass.primaryConstructor!!
            constructor.isAccessible = true
            val validParametersByName = constructor.parameters
                .filter { it.kind == KParameter.Kind.VALUE && it.name != null }
                .associateBy { it.name!!.lowercase() }

            val matchingParams = mutableListOf<Pair<KParameter, Any?>>()
            for (i in 1..rs.metaData.columnCount) {
                rs.metaData.getColumnLabel(i).replace("_", "").lowercase()
                    .let { colLabel ->
                        validParametersByName[colLabel]?.let { param ->
                            val res = mapField(i, rs, param)
                            matchingParams.add(res)
                        }
                    }
            }

            val paramsThatArePresent = matchingParams.map { it.first }.toHashSet()
            val nullablesThatAreAbsent = constructor.parameters
                .filter { !it.isOptional && it.type.isMarkedNullable && it !in paramsThatArePresent }
                .map { Pair(it, null) }

            val defaultableThatAreAbsent = constructor.parameters
                .filter { it.isOptional && !it.type.isMarkedNullable && it !in paramsThatArePresent }
                .toSet()

            val finalParams = (matchingParams + nullablesThatAreAbsent)
                .filterNot { it.first in defaultableThatAreAbsent }
                .toMap()
            constructor.callBy(finalParams)
        }
    }

    private fun mapField(index: Int, rs: ResultSet, param: KParameter): Pair<KParameter, Any?> {
        return if (param.type.isMarkedNullable && rs.getObject(index) == null) {
            Pair(param, null)
        }
        else if(param.type.isSubtypeOf(typeOf<Map<*,*>>())){
            Pair(param, mapper.readValue(rs.getString(index), Map::class.java))
        }
        else if(param.type.isSubtypeOf(typeOf<Set<String>>())){
            val array = rs.getArray(index)?.array as? Array<*>
            Pair(param, array?.mapNotNull { it.toString() }?.toSet() ?: emptySet())
        }
        // TODO Better implementation of string type array reads, it.length-1 is not optimal
        else if(param.type.isSubtypeOf(typeOf<List<String>>())){
            Pair(param, rs.getString(index).let { it.substring(1,it.length-1).replace("\"", "") }.split(","))
        }
        else if(param.type.isSubtypeOf(typeOf<List<*>>())){
            val genericType = (param.type.arguments.first().type?.javaType ?: Any::class.java) as Class<*>
            val value = rs.getString(index)
            val type = TypeFactory.defaultInstance().constructCollectionType(List::class.java, genericType)
            Pair(param, mapper.readValue(value, type))
        }
        else {
            when (val paramType = param.type.javaType) {
                Boolean::class.java -> {
                    Pair(param, rs.getBoolean(index))
                }
                String::class.java -> {
                    Pair(param, rs.getString(index))
                }
                Int::class.java -> {
                    Pair(param, rs.getInt(index))
                }
                Long::class.java -> {
                    Pair(param, rs.getLong(index))
                }
                SqlDate::class.java -> {
                    // Directly retrieve java.sql.Date
                    Pair(param, rs.getDate(index))
                }
                java.util.Date::class.java -> {
                    val value = rs.getObject(index)
                    val long = getTimestampUTC(index, value, param)
                    Pair(param, Date(long))
                }
                DateTime::class.java -> {
                    val value = rs.getObject(index)
                    val long = getTimestampUTC(index, value, param)
                    Pair(param, DateTime(long))
                }
                Timestamp::class.java -> {
                    Pair(param, rs.getTimestamp(index))
                }
                ZonedDateTime::class.java -> {
                    val value = rs.getObject(index)
                    val dt = getZonedDateTime(index, value, param)
                    Pair(param, dt)
                }
                Double::class.java -> {
                    Pair(param, rs.getDouble(index))
                }
                Float::class.java -> {
                    Pair(param, rs.getFloat(index))
                }
                PGpoint::class.java -> {
                    Pair(param, rs.getObject(index) as PGpoint)
                }
                else -> {
                    val value = rs.getString(index)
                    if ((param.type.javaType as Class<*>).isEnum) {
                        val enumClass = (param.type.javaType as Class<*>)
                        Pair(
                            param,
                            enumClass.getMethod("valueOf", String::class.java)
                                .invoke(null, value)
                        )
                    } else {
                        Pair(
                            param,
                            mapper.readValue(
                                value,
                                Class.forName(paramType.typeName)
                            )
                        )
                    }
                }
            }
        }
    }

    private fun getTimestampUTC(i: Int, value: Any?, param: KParameter): Long {
        return when (value) {
            is Timestamp -> value.time + TimeZone.getDefault().rawOffset
            is DateTimeOffset -> value.timestamp.time
            else -> throw RuntimeException("Type mismatch: Result set at column number $i " +
                    "cannot be converted to Long Type. param: $param")
        }
    }

    private fun getZonedDateTime(i: Int, value: Any?, param: KParameter): ZonedDateTime {
        return when (value) {
            is Timestamp -> ZonedDateTime.ofInstant(
                Instant.ofEpochMilli(value.time + TimeZone.getDefault().rawOffset), ZoneId.of(ZoneOffset.UTC.id)
            )
            is DateTimeOffset -> ZonedDateTime.ofInstant(
                Instant.ofEpochMilli(value.timestamp.time), ZoneId.of(
                    ZoneOffset.ofTotalSeconds(
                        TimeUnit.MINUTES.toSeconds(value.minutesOffset.toLong()).toInt()
                    ).id
                )
            )
            else -> throw RuntimeException("Type mismatch: Result set at column number $i " +
                    "cannot be converted to Long Type. param: $param")
        }
    }

    fun createParameterizedType(rawType: Class<*>, vararg typeArguments: Type): ParameterizedType {
        return object : ParameterizedType {
            override fun getRawType(): Type = rawType
            override fun getActualTypeArguments(): Array<out Type> = typeArguments
            override fun getOwnerType(): Type? = null
        }
    }
}

package com.udaan.competitive.core.dao

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.udaan.common.utils.toEpocMillis
import com.udaan.competitive.models.CompetitorLoginAccount
import com.udaan.competitive.core.models.DefaultRowMapper
import com.udaan.competitive.core.utils.NamedConstants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.jdbi.v3.core.Jdbi
import java.sql.Timestamp

@Singleton
class CompetitorLoginAccountDao @Inject constructor(
    @Named(NamedConstants.DB.ONLINE_COMP) private val jdbi: Jdbi,
    private val objectMapper: ObjectMapper
) {

    companion object {
        private const val TABLE_NAME = "competitor_login_account"
    }

    suspend fun createOrUpdate(account: CompetitorLoginAccount): CompetitorLoginAccount {
        return withContext(Dispatchers.IO) {
            jdbi.withHandle<Unit, Exception> { handle ->
                handle.createUpdate(
                    """
                    INSERT INTO $TABLE_NAME (
                        id, competitor_city_id, account_identifier_type, account_identifier,
                        supported_login_methods, competitor_specific_login_attributes,
                        state, created_at, updated_at
                    )
                    VALUES (
                        :id, :competitorCityId, :accountIdentifierType, :accountIdentifier,
                        CAST(:supportedLoginMethods AS text[]), CAST(:competitorSpecificLoginAttributes AS jsonb),
                        :state, :createdAt, :updatedAt
                    )
                    ON CONFLICT(id) DO UPDATE SET
                        competitor_city_id = EXCLUDED.competitor_city_id,
                        account_identifier_type = EXCLUDED.account_identifier_type,
                        account_identifier = EXCLUDED.account_identifier,
                        supported_login_methods = CAST(EXCLUDED.supported_login_methods AS text[]),
                        competitor_specific_login_attributes = CAST(EXCLUDED.competitor_specific_login_attributes AS JSONB),
                        state = EXCLUDED.state,
                        updated_at = EXCLUDED.updated_at
                    """
                )
                    .bind("id", account.id)
                    .bind("competitorCityId", account.competitorCityId)
                    .bind("accountIdentifierType", account.accountIdentifierType)
                    .bind("accountIdentifier", account.accountIdentifier)
                    .bind("supportedLoginMethods", account.supportedLoginMethods.toTypedArray())
                    .bind("competitorSpecificLoginAttributes", objectMapper.writeValueAsString((account.competitorSpecificLoginAttributes)))
                    .bind("state", account.state.name)
                    .bind("createdAt", Timestamp(account.createdAt.toEpocMillis()))
                    .bind("updatedAt", Timestamp(account.updatedAt.toEpocMillis()))
                    .execute()
            }
            account
        }
    }

    suspend fun getById(id: String): CompetitorLoginAccount? {
        return withContext(Dispatchers.IO) {
            jdbi.withHandle<CompetitorLoginAccount?, Exception> { handle ->
                handle.createQuery("SELECT * FROM $TABLE_NAME WHERE id = :id")
                    .bind("id", id)
                    .map(CompetitorLoginAccountMapper())
                    .firstOrNull()
            }
        }
    }

    suspend fun getLoginAccountsByCompetitorCityAndStates(
        competitorCityId: String,
        states: List<String>
    ): List<CompetitorLoginAccount> {
        return withContext(Dispatchers.IO) {
            jdbi.withHandle<List<CompetitorLoginAccount>, Exception> { handle ->
                handle.createQuery(
                    """
                    SELECT * FROM $TABLE_NAME 
                    WHERE competitor_city_id = :competitorCityId 
                    AND state = ANY(:states)
                    """
                )
                    .bind("competitorCityId", competitorCityId)
                    .bind("states", states.toTypedArray())
                    .map(CompetitorLoginAccountMapper())
                    .list()
            }
        }
    }

    suspend fun getLoginAccountsByCompetitorCityAndIdentifier(
        competitorCityId: String,
        identifier: String
    ): CompetitorLoginAccount? {
        return withContext(Dispatchers.IO) {
            jdbi.withHandle<CompetitorLoginAccount?, Exception> { handle ->
                handle.createQuery(
                    """
                    SELECT * FROM $TABLE_NAME 
                    WHERE competitor_city_id = :competitorCityId 
                    AND account_identifier = :identifier
                    """
                )
                    .bind("competitorCityId", competitorCityId)
                    .bind("identifier", identifier)
                    .map(CompetitorLoginAccountMapper())
                    .firstOrNull()
            }
        }
    }

    class CompetitorLoginAccountMapper : DefaultRowMapper<CompetitorLoginAccount>(CompetitorLoginAccount::class.java)
}

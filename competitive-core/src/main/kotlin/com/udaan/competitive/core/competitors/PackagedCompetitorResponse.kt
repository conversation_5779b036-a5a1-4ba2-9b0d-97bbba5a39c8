package com.udaan.competitive.core.competitors

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.udaan.competitive.core.competitors.hyperpure.PackagedHyperPureCategoryResponse
import com.udaan.competitive.core.competitors.jumbotail.PackagedJTProductResultsPage
import com.udaan.competitive.core.competitors.metro.MetroPackagedResponse
import com.udaan.competitive.core.competitors.ninjacart.PackagedNinjacartCategoryResponse
import com.udaan.competitive.core.competitors.shikhar.PackagedShikharCategoryPageResponse

/**
 * Base class for wrapping competitor API responses. Uses Jackson type information for proper
 * deserialization of competitor-specific response formats.
 *
 * Implementations:
 * - [PackagedJTProductResultsPage] - For Jumbotail product results
 * - [PackagedShikharCategoryPageResponse] - For Shikhar category data
 * - [MetroPackagedResponse] - For Metro category data
 * - [PackagedHyperPureCategoryResponse] - For HyperPure category data
 * - [PackagedNinjacartCategoryResponse] - For Ninjacart category data
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
@JsonSubTypes(
        JsonSubTypes.Type(PackagedJTProductResultsPage::class),
        JsonSubTypes.Type(PackagedShikharCategoryPageResponse::class),
        JsonSubTypes.Type(MetroPackagedResponse::class),
        JsonSubTypes.Type(PackagedHyperPureCategoryResponse::class),
        JsonSubTypes.Type(PackagedNinjacartCategoryResponse::class)
)
abstract class PackagedCompetitorResponse

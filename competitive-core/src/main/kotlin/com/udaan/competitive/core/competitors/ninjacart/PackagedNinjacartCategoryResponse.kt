package com.udaan.competitive.core.competitors.ninjacart

import com.udaan.competitive.core.competitors.PackagedCompetitorResponse

/**
 * Wrapper class for Ninjacart category response data. This class extends PackagedCompetitorResponse
 * to provide proper Jackson type information for deserialization.
 *
 * @property ninjacartCategoryResponse The actual Ninjacart category response containing products data
 */
internal data class PackagedNinjacartCategoryResponse(
    val ninjacartCategoryResponse: NinjacartCategoryResponse
) : PackagedCompetitorResponse()
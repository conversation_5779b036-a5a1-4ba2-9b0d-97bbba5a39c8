package com.udaan.competitive.core.offline.managers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.competitive.core.generateId
import com.udaan.competitive.core.models.SupplierCreateOrUpdateRequest
import com.udaan.competitive.core.offline.dao.SuppliersDao
import com.udaan.competitive.core.offline.models.LocationType
import com.udaan.competitive.core.offline.models.Supplier
import com.udaan.competitive.core.offline.models.SupplierStatus
import com.udaan.competitive.core.utils.isValidPhoneNumber
import com.udaan.competitive.models.City
import java.sql.Timestamp

@Singleton
class SupplierManager @Inject constructor(private val suppliersDao: SuppliersDao){

    suspend fun createOrUpdateSupplier(supplierCreateOrUpdateRequest: SupplierCreateOrUpdateRequest): Supplier {
        assert(isValidPhoneNumber(supplierCreateOrUpdateRequest.phoneNumber)) {
            "Phone number is not a valid one"
        }
        val activeSupplier =
            suppliersDao.getSupplierByPhoneNumber(phoneNumber = supplierCreateOrUpdateRequest.phoneNumber)

        val newOrUpdatedSupplier = activeSupplier?.copy(
            name = supplierCreateOrUpdateRequest.name,
            locationValue = supplierCreateOrUpdateRequest.city.name,
            address = supplierCreateOrUpdateRequest.address
        ) ?: createNewSupplier(supplierCreateOrUpdateRequest)
        return suppliersDao.createOrUpdateSupplier(newOrUpdatedSupplier)
    }

    private fun createNewSupplier(agentCreateOrUpdateRequest: SupplierCreateOrUpdateRequest): Supplier {
        return Supplier(
            id = generateId("SUP"),
            phoneNumber = agentCreateOrUpdateRequest.phoneNumber,
            name = agentCreateOrUpdateRequest.name,
            locationValue = agentCreateOrUpdateRequest.city.name,
            locationType = LocationType.CITY,
            status = SupplierStatus.ACTIVE,
            address = agentCreateOrUpdateRequest.address,
            geoLocation = null,
            createdAt = Timestamp(System.currentTimeMillis()),
            updatedAt = Timestamp(System.currentTimeMillis())
        )
    }

    suspend fun getSuppliers(city: City): List<Supplier> {
        return suppliersDao.getSuppliersByCity(city)
    }

    suspend fun getSupplierById(supplierId: String): Supplier {
        return suppliersDao.getSupplierById(supplierId)
    }
}
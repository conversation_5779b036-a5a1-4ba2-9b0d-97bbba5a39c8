package com.udaan.competitive.core.competitors.ninjacart

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.competitive.core.competitors.BaseCrawler
import com.udaan.competitive.core.manager.CrawlJobManager
import com.udaan.competitive.core.models.ParsePayload
import com.udaan.competitive.core.models.ProcessorQueues
import com.udaan.competitive.core.providers.EventEmitterProvider
import com.udaan.competitive.core.providers.Message
import com.udaan.competitive.core.utils.HttpClientV2
import com.udaan.competitive.core.utils.HttpClientV2Helpers.makeRequest
import com.udaan.competitive.core.utils.RequestMethod
import com.udaan.competitive.models.*
import java.net.URL

@Singleton
class NinjacartNonLoggedInCrawler @Inject constructor(
    private val crawlJobManager: CrawlJobManager,
    private val objectMapper: ObjectMapper,
    private val eventEmitterProvider: EventEmitterProvider
): BaseCrawler(Competitor.NINJACART, crawlJobManager) {

    companion object {
        private val log by logger()

        private const val HOST = "app.ninjacart.in"
        private const val USERAGENT = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) " +
                "AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"

        private const val FACILITY_ID = 2773
        private const val CATEGORY_URL = "https://$HOST/knowhere/skuCategory/listSku/v1"
        private const val PAGE_SIZE = 500
    }

    private fun getHeaders(): MutableMap<String, String> {
        return mutableMapOf(
            "Accept" to "application/json, text/plain, */*",
            "Accept-Language" to "en-GB,en;q=0.5",
            "Connection" to "keep-alive",
            "Content-Type" to "application/json",
            "Origin" to "https://$HOST",
            "Referer" to "https://$HOST/da-web/no-auth/listing",
            "Sec-Fetch-Dest" to "empty",
            "Sec-Fetch-Mode" to "cors",
            "Sec-Fetch-Site" to "same-origin",
            "Sec-GPC" to "1",
            "User-Agent" to USERAGENT
        )
    }

    private fun getRequestBody(facilityId: Int, offset: Int): String {
        val bodyAsMap = mutableMapOf(
            "offset" to offset,
            "limit" to PAGE_SIZE,
            "orderMode" to 1,
            "skuTypeIds" to mutableListOf(1),
            "customerTypeId" to 35,
            "categoryIds" to mutableListOf(1, 2),
            "languageId" to 1,
            "facilityId" to facilityId
        )
        return objectMapper.writeValueAsString(bodyAsMap)
    }

    private fun makeCategoryRequestForFacilityId(
        httpClientV2: HttpClientV2,
        facilityId: Int,
        offset: Int
    ): NinjacartCategoryResponse? {
        // pass proxy once proxy server has capability for gzip
        val response = httpClientV2.makeRequest(
            url = URL(CATEGORY_URL),
            headers = getHeaders(),
            method = RequestMethod.POST,
            body = getRequestBody(facilityId, offset),
            proxy = null
        ) ?: return null
        return objectMapper.readValue<NinjacartCategoryResponse>(response)
    }

    override suspend fun crawlCategory(cJobID: String, categoryId: String, accountId: String, city: City, proxy: Proxy?) {
        log.info("crawlByBrandNew cJobID:$cJobID on brand: $categoryId with account:$accountId using proxy: ${proxy?.id}")
        val httpClient = HttpClientV2.buildClient(authorization = null)
        var hasMoreProducts = true
        var offSet = 0
        while (hasMoreProducts) {
            val response = makeCategoryRequestForFacilityId(httpClient, FACILITY_ID, offSet)
            if (response == null) {
                log.warn(
                    "Received null response from server while crawling categoryId: {} with offset: {} using customerId: {} and proxy: {}",
                    categoryId, offSet, FACILITY_ID, proxy?.readable()
                )
                hasMoreProducts = false
            } else {
                emitResponse(cJobID, city, response)
                // to decide if more pagination call is required
                hasMoreProducts = response.data.size == PAGE_SIZE
                if (hasMoreProducts) {
                    offSet += PAGE_SIZE
                }
            }
        }
    }

    private suspend fun emitResponse(cJobID: String, city: City, ninjacartCategoryResponse: NinjacartCategoryResponse) {
        val parsePayload = ParsePayload(
            competitor = Competitor.NINJACART,
            city = city,
            jobRunId = cJobID,
            response = PackagedNinjacartCategoryResponse(ninjacartCategoryResponse)
        )
        return eventEmitterProvider.sendMessages(
            ProcessorQueues.ParserQueue, listOf(
                Message(
                    id = null,
                    payload = parsePayload
                )
            )
        )
    }

    override suspend fun crawlBrand(
        cJobID: String,
        brandId: String,
        accountId: String,
        city: City,
        proxy: Proxy?
    ) {
        TODO("Not yet implemented")
    }

}

package com.udaan.competitive.core.competitors.hyperpure

import com.udaan.competitive.core.competitors.PackagedCompetitorResponse

/**
 * Wrapper class for HyperPure category response data. This class extends PackagedCompetitorResponse
 * to provide proper Jackson type information for deserialization.
 *
 * @property hyperPureCategoryResponse The actual HyperPure category response containing products data
 */
internal data class PackagedHyperPureCategoryResponse(
    val hyperPureCategoryResponse: HyperPureCategoryResponse
) : PackagedCompetitorResponse()
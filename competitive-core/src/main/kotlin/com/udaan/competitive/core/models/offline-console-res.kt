package com.udaan.competitive.core.models

import com.udaan.competitive.models.GeoLoc

data class AgentBirdsEyeView(
    val agents: List<AgentView>,
)

data class AgentView(
    val id: String,
    val name: String,
    val phone: String,
    val lastActiveAt: Long?,
    val suppliersVisited: Int,
    val suppliersAlloted: Int,
    val lastLocation: GeoLoc?
)

data class SupplierBirdsEyeView(
    val suppliers: List<SupplierView>
)

data class SupplierView(
    val id: String,
    val name: String,
    val address: String,
    val geoLocation: GeoLoc?,
    val lastVisitedAt: Long?,
    val totalProducts: Int?,
    val priceCollectedProducts: Int?,
)

data class ProductBirdsEyeView(
    val products: List<ProductView>
)

data class ProductView(
    val udCatalogId: String,
    val priceInPaise: Int?,
    val lastCalculatedAt: Long?,
    val productId: String,
)
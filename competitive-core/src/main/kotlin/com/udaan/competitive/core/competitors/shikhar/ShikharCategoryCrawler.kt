package com.udaan.competitive.core.competitors.shikhar

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.competitive.core.competitors.BaseCrawler
import com.udaan.competitive.core.dao.CompetitiveDataRepository
import com.udaan.competitive.core.dao.CompetitorCatalogRepository
import com.udaan.competitive.core.dao.OnlineCompSessionDao
import com.udaan.competitive.core.manager.CrawlJobManager
import com.udaan.competitive.core.utils.TelemetryHelper
import com.udaan.competitive.models.*
import com.udaan.instrumentation.TelemetryScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay

/**
 * Crawler implementation for Shikhar competitor's category-based product data. Handles crawling and
 * recording of product data from <PERSON><PERSON>'s catalog.
 *
 * @property crawlJobManager Manager for tracking crawl job status
 * @property competitiveDataRepository Repository for storing competitive data points
 * @property competitorCatalogRepository Repository for managing competitor catalog entries
 */
@Singleton
class ShikharCategoryCrawler
@Inject
constructor(
        private val crawlJobManager: CrawlJobManager,
        private val competitiveDataRepository: CompetitiveDataRepository,
        private val competitorCatalogRepository: CompetitorCatalogRepository,
        private val onlineCompSessionDao: OnlineCompSessionDao
) : BaseCrawler(Competitor.SHIKHAR, crawlJobManager) {

    companion object {
        private val LOG by logger()
    }

    override suspend fun crawlBrand(
            cJobID: String,
            brandId: String,
            accountId: String,
            city: City,
            proxy: Proxy?
    ) {
        TODO("Not yet implemented")
    }

    /**
     * Records competitive data for a single Shikhar product
     *
     * @param hulId Unique hub/location identifier for Shikhar
     * @param jobId Current crawl job identifier
     * @param product Product data to record
     * @param city City for which data is being recorded
     */
    private suspend fun recordCompetitiveData(
            hulId: String,
            jobId: String,
            product: ShikharProduct,
            city: City
    ) {
        try {

            val currentCatalog = getCurrentCatalog(product.itemvarient_desc, city)

            val catalog =
                    if (currentCatalog == null) {
                        LOG.info("New catalog found: ${product.itemvarient_desc}")
                        competitorCatalogRepository.createOrUpdate(
                                product.toCompetitorCatalog(city)
                        )
                    } else {
                        currentCatalog
                    }

            val competitiveData =
                    product.toCompetitiveData(
                            jobRunId = jobId,
                            city = city,
                            catalogId = catalog.id,
                            trackingMap = mapOf("hulId" to hulId)
                    )
            if (competitiveData != null) {
                competitiveDataRepository.createOrUpdate(competitiveData)
            } else {
                LOG.warn("No price found for ${product.itemvarient_desc}")
            }
        } catch (e: Exception) {
            TelemetryHelper.trackException(
                    e = e,
                    labels =
                            mapOf(
                                    "jobId" to jobId,
                                    "product" to product.itemvarient_desc,
                                    "city" to city.name
                            )
            )
        }
    }

    /**
     * Retrieves existing catalog entry for a product
     *
     * @param identifier Product identifier (usually product name)
     * @param city City to search catalog in
     * @return [CompetitorCatalog] if found, null otherwise
     */
    private suspend fun getCurrentCatalog(identifier: String, city: City): CompetitorCatalog? {
        val catalogItems =
                competitorCatalogRepository.getCatalogItemForCity(
                        identifier = identifier,
                        city = city.name,
                        identifierType = CompetitorIdentifierType.PRODUCT_NAME.name,
                        competitor = Competitor.SHIKHAR.name,
                        variant = null
                )
        if (catalogItems.size > 1) {
            LOG.warn("Seeing multiple catalog items for the same city ${city.name} - $identifier ")
        }
        return catalogItems.firstOrNull()
    }

    /**
     * Crawls all products in a given category. Retrieves paginated results and processes them in
     * parallel.
     *
     * @param cJobID Crawl job identifier
     * @param categoryId ID of category to crawl
     * @param accountId Account to use for authentication
     * @param city City to crawl data for
     * @param proxy Optional proxy to use for crawling
     * @throws IllegalStateException if no active session is found for the account
     */
    override suspend fun crawlCategory(
            cJobID: String,
            categoryId: String,
            accountId: String,
            city: City,
            proxy: Proxy?
    ) {
        val session = onlineCompSessionDao.getLatestActiveByAccountId(accountId)
        val authToken =
                session?.authToken
                        ?: throw IllegalStateException("No active session found for $accountId")
        val hulId = (session.competitorSpecificAttributes as ShikharSessionAttributes).hulId
        val shikharClient = ShikharClient(accessToken = authToken, hulId = hulId, proxy = proxy)
        var startIndex = 0
        while (startIndex != -1) {
            val paginatedResponse =
                    shikharClient.getCategoryProducts(categoryId, startIndex).await()
            startIndex = paginatedResponse?.start ?: -1
            val products = paginatedResponse?.productgroup ?: emptyList()
            TelemetryScope.launch {
                products.flatMap { it.products }.parallelMap(
                                context = Dispatchers.IO,
                                parallelismFactor = 10
                        ) { product -> recordCompetitiveData(hulId, cJobID, product, city) }
            }
            delay(1000)
        }
        LOG.info("Completed crawl of $categoryId for $city using $accountId")
    }
}

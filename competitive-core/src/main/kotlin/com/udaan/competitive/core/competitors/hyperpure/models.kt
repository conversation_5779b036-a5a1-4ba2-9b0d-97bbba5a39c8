package com.udaan.competitive.core.competitors.hyperpure

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.udaan.competitive.core.generateId
import com.udaan.competitive.core.utils.StringUtils.getPriceWithoutRupeeSymbol
import com.udaan.competitive.core.utils.TelemetryHelper
import com.udaan.competitive.core.utils.cleanPriceInRupees
import com.udaan.competitive.core.utils.isInvalidPrice
import com.udaan.competitive.models.*

@JsonIgnoreProperties(ignoreUnknown = true)
internal data class HyperPureCategoryResponse(
    val response: CategoryProductResponse
)

@JsonIgnoreProperties(ignoreUnknown = true)
internal data class CategoryProductResponse(
    @JsonProperty("Products", "products")
    val Products: List<HyperPureCategoryProduct>,
    @JsonProperty("HasNextPage", "hasNextPage")
    val HasNextPage: Boolean
)

@JsonIgnoreProperties(ignoreUnknown = true)
internal data class HyperPureCategoryProduct(
    val Id: Int,
    val Name: String,
    val ImagePath: String,
    val Quantity: Quantity,
    val Price: Price,
    val OffersV3: List<OfferV3>?,
    val SuperSaverSlab: OfferV3?,
    val EffectiveUnitPrice: EffectiveUnitPrice?,
    val IsInStock: Boolean,
    val PriceType: String,
    val WarehouseCode: String,
    val Slug: String,
    val ParentCategorySlug: String,
    val CategoryName: String,
    val Brand: String?,
    val Vertical: String?,
    val Description: String?
) {

    private val quantity: Double by lazy {
        Quantity.DisplayValue
            .split(Quantity.Unit)
            .firstOrNull()
            ?.toDouble() ?: 1.0
    }

    private val eachesQty: Double by lazy {
        quantity / getNoOfEachesInAssortment()
    }

    private fun getNoOfEachesInAssortment(): Int {
        val regex = Regex("Pack of (\\d+)")
        val matchResult = regex.find(Name)
        return matchResult?.groupValues?.get(1)?.toIntOrNull() ?: 1
    }

    private val offerLadders: List<Pair<Double, Double>> by lazy {
        val extracted = OffersV3
            ?.map { it.toLadder() }
            .orEmpty()
            .plus(SuperSaverSlab?.toLadder())
            .filterNotNull()
            .sortedBy { it.first }
            .filter { it.first >= eachesQty }
        return@lazy extracted
    }


    fun toCompetitorCatalog(city: City): CompetitorCatalog? {
        if (quantity == 0.0) return null
        return CompetitorCatalog(
            id = generateId("CC"),
            city = city,
            competitor = Competitor.HYPERPURE,
            competitorIdentifierType = CompetitorIdentifierType.PRODUCT_NAME,
            identifier = this.Name,
            vertical = this.Vertical,
            brand = this.Brand,
            marketCaseSize = null,
            packOf = null,
            variant = null,
            active = true,
            createdAt = System.currentTimeMillis(),
            createdBy = null,
            competitorCategory = this.CategoryName,
            competitorVertical = null,
            competitorTitle = this.Name,
            competitorWeight = quantity,
            competitorWeightUnit = Quantity.Unit,
            competitorBrand = this.Brand,
            additionalInfo = null,
            competitorProductId = Slug,
            crawlFrequencyType = null,
            crawlScheduleId = null
        )
    }

    private val price by lazy {
        Price.Price
            .getPriceWithoutRupeeSymbol()
            ?.cleanPriceInRupees()
            ?.addTax()
            ?.toPerUnitPrice()
    }

    private fun getUnitLadder(): LadderPrice? {
        val priceExtracted = price ?: return null
        if (priceExtracted == 0.0) return null

        return if (offerLadders.isEmpty()) {
            LadderPrice(
                minQty = 1,
                maxQty = 9999,
                price = priceExtracted
            )
        } else {
            val firstLadderMaxQty = (offerLadders.first().first / eachesQty).toInt()
            // quantity threshold where the first ladder pricing tier begins.
            LadderPrice(
                minQty = 1,
                maxQty = firstLadderMaxQty - 1,
                price = priceExtracted
            )
        }
    }

    private fun getLadderPrices(): List<LadderPrice> {
        val ladders = listOf<LadderPrice>()
        val unitLadder = getUnitLadder()
        val offerLadderPrices = offerLaddersToLadderPrices()
        return ladders.plus(unitLadder).plus(offerLadderPrices).filterNotNull()
    }

    private fun offerLaddersToLadderPrices(): List<LadderPrice> {
        return offerLadders.mapIndexed { index, slab ->
            val minQty = (slab.first / eachesQty).toInt()
            val slabPrice = (slab.second.addTax() * eachesQty).cleanPriceInRupees()
            check(slabPrice != 0.0) { "Slab price is coming up as 0.0 for $Name" }
            val maxQty = if (index < offerLadders.lastIndex) {
                (offerLadders[index + 1].first / eachesQty).toInt() - 1
            } else {
                9999
            }

            LadderPrice(
                minQty = minQty,
                maxQty = maxQty,
                price = slabPrice
            )
        }
    }

    private fun Double.addTax(): Double {
        val taxPercent = Price.getTaxPercentage()
        return if (taxPercent == null) this.cleanPriceInRupees()
        else (this * (1 + taxPercent / 100)).cleanPriceInRupees()
    }

    private fun Double.toPerUnitPrice(): Double? {
        if (quantity == 0.0) return null
        val eachesInAssortment = getNoOfEachesInAssortment()
        return (this / eachesInAssortment).cleanPriceInRupees()
    }

    fun toCompetitiveData(
        jobRunId: String,
        city: City,
        catalogId: String,
        trackingMap: Map<String, String>
    ): CompetitiveData? {
        if (price == null) {
            TelemetryHelper.trackException(
                IllegalArgumentException("Price is found to be null"),
                labels = mapOf(
                    "productName" to Name,
                    "catalogId" to catalogId,
                    "jobRunId" to jobRunId,
                    "price" to Price.Price
                )
            )
            return null
        }
        return CompetitiveData(
            id = generateId("CD"),
            catalogId = catalogId,
            competitorCityId = "${Competitor.HYPERPURE.name}_${city.name}",
            competitorSpecificAttributes = trackingMap + mapOf(
                "warehouseCode" to WarehouseCode,
                "priceType" to PriceType
            ),
            createdAt = System.currentTimeMillis(),
            currentPrice = price,
            competitorJobRunId = jobRunId,
            ladderPrices = getLadderPrices(),
            moq = Quantity.MinimumOrderQuantity,
            mrp = Name.extractMRP(),
            sellerName = null,
            availableQuantity = null,
            screenshotUrl = ImagePath,
            offerData = null,
            productImageUrl = ImagePath,
            remarks = null,
            isSuccessful = true,
            outOfStock = IsInStock.not() || price.isInvalidPrice()
        )
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
internal data class Quantity(
    val CartQuantity: Int,
    val DisplayValue: String,
    val Unit: String,
    val MinimumOrderQuantity: Int
)

@JsonIgnoreProperties(ignoreUnknown = true)
internal data class Price(
    val Label: String,
    val Price: String,
    val CompareAtPrice: String,
    val Unit: String,
    val SubHeading: String,
    val TaxRate: String,
    val PriceVal: Int?,
    val UnitPricePerUom: String,
    val CompareAtPriceV2: String
) {
    fun getTaxPercentage(): Double? {
        return if (TaxRate.isBlank() || TaxRate.contains("%").not()) null
        else TaxRate.split("%").firstOrNull()?.toDouble()
    }
}

internal fun String.extractMRP(): Double? {
    return try {
        val regex = Regex("""MRP\s*[-:;\s]\s*(\d+(?:\.\d+)?)""")
        return regex.find(this)?.groupValues?.get(1)?.toDoubleOrNull()
    } catch (e: Exception) {
        TelemetryHelper.trackException(e, labels = mapOf("productName" to this))
        null
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
internal data class OfferV3(
    val Text: String = "",
    val TextColor: String = ""
) {

    fun toLadder(): Pair<Double, Double>? {
        return try {
            val price = Regex("""₹([\d,]+(?:\.\d+)?)""")
                .find(Text)
                ?.groupValues
                ?.get(1)
                ?.replace(",", "")
                ?.toDoubleOrNull() ?: return null
            val quantity = Regex("for\\s+([\\d.]+)")
                .find(Text)
                ?.groupValues
                ?.get(1)
                ?.toDoubleOrNull() ?: return null
            Pair(quantity, price)
        } catch (ex: Exception) {
            TelemetryHelper.trackException(ex, labels = mapOf("offerText" to this.Text))
            null
        }
    }

}

@JsonIgnoreProperties(ignoreUnknown = true)
internal data class EffectiveUnitPrice(
    val Label: String,
    val Price: String,
    val CompareAtPrice: String
)

data class GenerateOtpResponse(val response: ResponseMessage)
data class ResponseMessage(val message: String)

internal data class VerifyOtpWrapper(
    val response: VerifyOtpResponse
)

internal data class VerifyOtpResponse(
    @JsonProperty("PhoneNumber")
    val phoneNumber: String,

    @JsonProperty("OutletId")
    val outletId: Int
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class FetchUserResponse(val response: Account)
data class Account(val account: AccountDetails, val outlet: OutletDetails)
data class AccountDetails(val id: Number, val organizationName: String)
data class OutletDetails(val id: Number, val outletName: String)

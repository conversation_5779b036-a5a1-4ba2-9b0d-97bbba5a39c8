package com.udaan.competitive.core.competitors.metro

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.competitive.core.competitors.BaseParser
import com.udaan.competitive.core.competitors.PackagedCompetitorResponse
import com.udaan.competitive.core.dao.CompetitiveDataRepository
import com.udaan.competitive.core.dao.CompetitorCatalogRepository
import com.udaan.competitive.core.models.OnlinePriceCapturePayload
import com.udaan.competitive.core.models.ProcessorQueues
import com.udaan.competitive.core.providers.EventEmitterProvider
import com.udaan.competitive.core.providers.Message
import com.udaan.competitive.models.City
import com.udaan.competitive.models.CompetitiveData
import com.udaan.competitive.models.Competitor
import org.slf4j.Logger

@Singleton
class MetroParser @Inject constructor(
    private val competitorCatalogRepository: CompetitorCatalogRepository,
    private val competitiveDataRepository: CompetitiveDataRepository,
    private val eventEmitterProvider: EventEmitterProvider
): BaseParser(competitorCatalogRepository, Competitor.METRO) {

    override val logger: Logger by logger()

    override suspend fun parseAndRecord(
        city: City,
        jobrunId: String,
        packagedCompetitorResponse: PackagedCompetitorResponse
    ) {
        if (packagedCompetitorResponse !is MetroPackagedResponse) {
            logger.error("Unknown response type: ${packagedCompetitorResponse.javaClass.simpleName}")
            return
        }
        packagedCompetitorResponse.items.forEach { item ->
            logger.info("Processing item: ${item.uid} - ${item.name}")
            val competitiveData = recordCompetitiveData(
                jobId = jobrunId,
                item = item,
                city = city,
                availabilityMap = packagedCompetitorResponse.availabilityMap,
                ladderPricingResponse = packagedCompetitorResponse.ladderPricingResponse
            )
            eventEmitterProvider.sendMessages(
                queue = ProcessorQueues.OnlinePriceCaptureQueue,
                messages = listOf(
                    Message(
                        id = competitiveData.catalogId,
                        payload = OnlinePriceCapturePayload(
                            id = competitiveData.id,
                            compProductId = competitiveData.catalogId,
                            capturedAt = System.currentTimeMillis(),
                            competitorCityId = competitiveData.competitorCityId,
                            parentJobRunId = jobrunId
                        )
                    )
                )
            )
        }
    }

    private suspend fun recordCompetitiveData(
        jobId: String,
        item: MetroItem,
        city: City,
        availabilityMap: Map<Long, MetroItemAvailability>,
        ladderPricingResponse: LadderPricingResponse?
    ): CompetitiveData {
        val catalog = findOrUpsertCatalogEntry(item.toCompetitorCatalog(city))
        val packSize = extractPackSizeFromName(item.name)
        val mrp = item.price?.marked?.min
        val ladderPrices = ladderPricingResponse?.availableLadders
            ?.filterValues { it.isNotEmpty() }
            ?.get(item.uid.toString())
            ?.toLadderPrices(packSize, mrp)
        val competitiveData = item.toCompetitiveData(
            jobRunId = jobId,
            catalogId = catalog.id,
            city = city,
            ladderPrices = ladderPrices,
            itemAvailability = availabilityMap[item.uid],
            packSize = packSize
        )
        logger.info("Recording competitive data for item: ${item.uid} - ${item.name}")
        return competitiveDataRepository.createOrUpdate(competitiveData)
    }
}
package com.udaan.competitive.core.dao

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.competitive.models.City
import com.udaan.competitive.models.Competitor
import com.udaan.competitive.models.CompetitorCatalog
import com.udaan.cosmosdb.utils.CosmosDbDao
import com.udaan.cosmosdb.utils.makeSqlQuerySpec
import kotlinx.coroutines.flow.toList

@Singleton
class CompetitorCatalogRepository @Inject constructor(private val objectMapper: ObjectMapper) {

    private val documentDbDao by lazy {
        CosmosDbDao(
            configKey = "fp-pricing",
            databaseName = dbname,
            containerName = competitor_catalog_table
        )
    }

    private fun CompetitorCatalog.toDocument() = objectMapper.convertValue(this, ObjectNode::class.java)
    private fun ObjectNode.toCompetitorCatalog() = objectMapper.convertValue(this, CompetitorCatalog::class.java)

    suspend fun createOrUpdate(competitorCatalog: CompetitorCatalog): CompetitorCatalog {
        return documentDbDao
            .createOrUpdateItem(competitorCatalog.toDocument()).toCompetitorCatalog()
    }

    suspend fun getCatalogItemForCity(
        identifier: String,
        identifierType: String,
        city: String,
        competitor: String,
        variant: String?
    ): List<CompetitorCatalog> {
        var query = """
                    select * from c 
                    where c.city="$city" and c.competitor="$competitor" and c.identifier="$identifier" 
                    and c.competitorIdentifierType="$identifierType" and c.active = true
                    """
        if(!variant.isNullOrBlank()){
            query +=  """ and c.variant='$variant'"""
        }
        return documentDbDao
            .queryItems(
                "getCatalogItemForCity",
                makeSqlQuerySpec(query.trimIndent())
            ).toList().map { it.toCompetitorCatalog() }
    }

    suspend fun getCatalogItemForCityWithoutIdentifierType(
        identifier: String,
        city: City,
        competitor: Competitor,
        variant: String?
    ): List<CompetitorCatalog> {
        var query = """
                    select * from c 
                    where c.city="$city" and c.competitor="$competitor" and c.identifier="$identifier" 
                    and c.active = true
                    """
        if(!variant.isNullOrBlank()){
            query +=  """ and c.variant='$variant'"""
        }
        return documentDbDao
            .queryItems(
                "getCatalogItemForCity",
                makeSqlQuerySpec(query.trimIndent())
            ).toList().map { it.toCompetitorCatalog() }
    }

    suspend fun getById(id: String): CompetitorCatalog? {
        return documentDbDao.getItem(id, id)?.toCompetitorCatalog()
    }
}

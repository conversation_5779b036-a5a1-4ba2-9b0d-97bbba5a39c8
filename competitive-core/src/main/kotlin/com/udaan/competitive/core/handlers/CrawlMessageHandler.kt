package com.udaan.competitive.core.handlers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.competitive.core.manager.ProxyManager
import com.udaan.competitive.core.models.CrawlTrigger
import com.udaan.competitive.core.providers.CrawlerProvider

/**
 * Handles crawl message processing for competitor data collection. Responsible for managing proxy
 * selection and delegating crawl operations to appropriate crawlers.
 *
 * @property proxyManager Manager for proxy selection and management
 * @property crawlerProvider Provider that supplies appropriate crawlers based on competitor
 */
@Singleton
class CrawlMessageHandler
@Inject
constructor(private val proxyManager: ProxyManager, private val crawlerProvider: CrawlerProvider) {

    companion object {
        private val LOG by logger()
    }

    /**
     * Handles a crawl trigger by selecting an active proxy and initiating the crawl operation
     *
     * @param crawlTrigger The trigger containing crawl job details
     */
    suspend fun handleCrawlTrigger(crawlTrigger: CrawlTrigger) {
//        val activeProxy = proxyManager.getARandomActiveProxy()
//        LOG.info("Using proxy ${activeProxy.id} for crawling ${crawlTrigger.ccJobId}")
        val crawler = crawlerProvider.getCrawlerByCompetitor(crawlTrigger.competitor)
        crawler.crawl(crawlTrigger, proxy = null)
    }
}

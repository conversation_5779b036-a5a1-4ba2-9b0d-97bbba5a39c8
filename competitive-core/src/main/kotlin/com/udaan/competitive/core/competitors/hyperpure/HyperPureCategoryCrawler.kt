package com.udaan.competitive.core.competitors.hyperpure

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.competitive.core.competitors.BaseCrawler
import com.udaan.competitive.core.dao.OnlineCompSessionDao
import com.udaan.competitive.core.manager.CrawlJobManager
import com.udaan.competitive.core.models.ParsePayload
import com.udaan.competitive.core.models.ProcessorQueues
import com.udaan.competitive.core.providers.EventEmitterProvider
import com.udaan.competitive.core.providers.Message
import com.udaan.competitive.core.utils.HttpClientV2
import com.udaan.competitive.core.utils.HttpClientV2Helpers.makeRequest
import com.udaan.competitive.core.utils.RequestMethod
import com.udaan.competitive.core.utils.StringUtils.parameterize
import com.udaan.competitive.models.AccessToken
import com.udaan.competitive.models.City
import com.udaan.competitive.models.Competitor
import com.udaan.competitive.models.CompetitorSpecificAttributes
import com.udaan.competitive.models.HyperPureSessionAttributes
import com.udaan.competitive.models.Proxy
import java.net.URL
import java.util.*

@Singleton
class HyperPureCategoryCrawler @Inject constructor(
    private val crawlJobManager: CrawlJobManager,
    private val objectMapper: ObjectMapper,
    private val onlineCompSessionDao: OnlineCompSessionDao,
    private val eventEmitterProvider: EventEmitterProvider
) : BaseCrawler(Competitor.HYPERPURE, crawlJobManager) {

    companion object {
        private val LOG by logger()
        private const val HOST = "www.hyperpure.com"
        private const val API_HOST = "api.hyperpure.com"
        private const val USERAGENT = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) " +
                "AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
    }

    private fun getHeaders(authToken: String, outletId: Number): Map<String, String> {
        return mapOf(
            "APIVersion" to "11.8",
            "Accept" to "application/json, text/plain, */*",
            "Accept-Language" to "en-GB,en;q=0.9",
            "AppType" to "mweb",
            "Authorization" to "Bearer $authToken",
            "Connection" to "keep-alive",
            "DeviceId" to "a65b76c8-0eb5-411f-b2ac-fe5de6231da7_uuid",
            "DeviceName" to "MacOS_Safari",
            "HeaderRoute" to "v2",
            "Origin" to HOST,
            "Referer" to "$HOST/",
            "Sec-Fetch-Dest" to "empty",
            "Sec-Fetch-Mode" to "cors",
            "Sec-Fetch-Site" to "same-site",
            "Sec-GPC" to "1",
            "User-Agent" to USERAGENT,
            "X-Client" to "consumer",
            "X-OutletId" to outletId.toString(),
            "X-TrackingId" to UUID.randomUUID().toString()
        )
    }

    override suspend fun crawlBrand(cJobID: String, brandId: String, accountId: String, city: City, proxy: Proxy?) {
        TODO("Not yet implemented")
    }


    private suspend fun emitResponse(cJobID: String, city: City, hyperPureCategoryResponse: HyperPureCategoryResponse) {
        val parsePayload = ParsePayload(
            competitor = Competitor.HYPERPURE,
            city = city,
            jobRunId = cJobID,
            response = PackagedHyperPureCategoryResponse(hyperPureCategoryResponse)
        )
        return eventEmitterProvider.sendMessages(
            ProcessorQueues.ParserQueue, listOf(
                Message(
                    id = null,
                    payload = parsePayload
                )
            )
        )
    }

    private fun buildCategoryURL(categoryId: String, pageNumber: Int, parentReferenceId: String, outletId: Number): String {
        return (
                "https://api.hyperpure.com/consumer/v2/search" +
                        "?outletId={outletId}&pageNo={pageNo}&categoryIds={categoryId}" +
                        "&sourcePage=CATEGORY_PAGE&entity_id=1&entity_type=category_grid" +
                        "&parent_reference_id={parentReferenceId}" +
                        "&parent_reference_type={parentReferenceId}" +
                        "&search_source=&source_page=&sub_reference_id=&sub_reference_type=" +
                        "&fetchThroughV2=false&searchDebugFlag=false" +
                        "&onlyInStock=false&getGlobalCatalog=false"
                )
                    .parameterize(
                        mapOf(
                            "outletId" to outletId.toString(),
                            "pageNo" to pageNumber.toString(),
                            "categoryId" to categoryId,
                            "parentReferenceId" to parentReferenceId
                        )
                    )
    }

    private fun makeCategoryRequest(
        httpClientV2: HttpClientV2,
        proxy: Proxy?,
        categoryId: String,
        headers: Map<String, String>,
        pageNumber: Int,
        parentReferenceId: String,
        outletId: Number
    ): HyperPureCategoryResponse? {
        val categoryURL = buildCategoryURL(categoryId, pageNumber, parentReferenceId, outletId)
        val response = httpClientV2.makeRequest(
            url = URL(categoryURL),
            headers = headers,
            method = RequestMethod.GET,
            proxy = proxy
        ) ?: return null
        return objectMapper.readValue<HyperPureCategoryResponse>(response)
    }

    private fun makeAuthorization(authToken: String, accountId: Number): AccessToken {
        return AccessToken(
            userId = Pair(CompetitorSpecificAttributes.USER_ID.key, accountId.toString()),
            accessToken = Pair(CompetitorSpecificAttributes.AUTH_TOKEN.key, authToken),
            mobile = 100 //Adding a dummy value as this is not required for JT
        )
    }

    override suspend fun crawlCategory(
        cJobID: String,
        categoryId: String,
        accountId: String,
        city: City,
        proxy: Proxy?
    ) {
        val session = onlineCompSessionDao.getLatestActiveByAccountId(accountId)
            ?: error("No active session found for accountId: $accountId")
        val sessionAttributes = session.competitorSpecificAttributes as? HyperPureSessionAttributes
            ?: error("Invalid session attributes for accountId: $accountId")
        val sessionAuthToken = checkNotNull(session.authToken) { "Auth token is null for accountId: $accountId" }
        val sessionOutletId = sessionAttributes.outletId
        val sessionAccountId = sessionAttributes.accountId
        val httpClient = HttpClientV2.buildClient(authorization = makeAuthorization(sessionAuthToken, sessionAccountId))
        val headers = getHeaders(sessionAuthToken, sessionOutletId)
        var hasMoreProducts = true
        var pageNumber = 1
        val parentReferenceId = UUID.randomUUID().toString() + System.nanoTime().toString()
        while (hasMoreProducts) {
            val response = makeCategoryRequest(httpClient, proxy, categoryId, headers, pageNumber, parentReferenceId, sessionOutletId)
            if (response == null) {
                LOG.error(
                    "Received null response from server while crawling categoryId: {} with offset: {} using proxy: {}",
                    categoryId, pageNumber, proxy?.readable()
                )
                hasMoreProducts = false
            } else {
                emitResponse(cJobID, city, response)
                // to decide if more pagination call is required
                hasMoreProducts = response.response.HasNextPage
                if (hasMoreProducts) {
                    pageNumber += 1
                }
            }
        }
    }

}

package com.udaan.competitive.core.controller

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.competitive.core.models.AgentBirdsEyeView
import com.udaan.competitive.core.models.AgentCreateOrUpdateRequest
import com.udaan.competitive.core.models.AgentSupplierMappingRequest
import com.udaan.competitive.core.models.AgentView
import com.udaan.competitive.core.models.ProductBirdsEyeView
import com.udaan.competitive.core.models.ProductCreateOrUpdateRequest
import com.udaan.competitive.core.models.ProductPricesView
import com.udaan.competitive.core.models.ProductToUdCatalogMappingRequest
import com.udaan.competitive.core.models.ProductView
import com.udaan.competitive.core.models.SupplierBirdsEyeView
import com.udaan.competitive.core.models.SupplierCreateOrUpdateRequest
import com.udaan.competitive.core.models.SupplierProductMappingRequest
import com.udaan.competitive.core.models.SupplierView
import com.udaan.competitive.core.offline.managers.AgentManager
import com.udaan.competitive.core.offline.managers.AgentSupplierMappingManager
import com.udaan.competitive.core.offline.managers.OfflineProductManager
import com.udaan.competitive.core.offline.managers.ProductUdCatalogMappingManager
import com.udaan.competitive.core.offline.managers.SupplierManager
import com.udaan.competitive.core.offline.managers.SupplierProductMappingManager
import com.udaan.competitive.core.offline.managers.VisitManager
import com.udaan.competitive.core.offline.models.Agent
import com.udaan.competitive.core.offline.models.AgentSupplierMapping
import com.udaan.competitive.core.offline.models.OfflineProduct
import com.udaan.competitive.core.offline.models.ProductUdCatalogMapping
import com.udaan.competitive.core.offline.models.Supplier
import com.udaan.competitive.core.offline.models.SupplierProductMapping
import com.udaan.competitive.core.offline.models.Visit
import com.udaan.competitive.core.offline.repo.OfflineRepo
import com.udaan.competitive.core.utils.TimeHelpers
import com.udaan.competitive.models.City
import com.udaan.competitive.models.GeoLoc
import com.udaan.instrumentation.TelemetryScope

@Singleton
class OfflineCompConsoleController @Inject constructor(
    private val agentManager: AgentManager,
    private val supplierManager: SupplierManager,
    private val offlineProductManager: OfflineProductManager,
    private val agentSupplierMappingManager: AgentSupplierMappingManager,
    private val supplierProductMappingManager: SupplierProductMappingManager,
    private val productUdCatalogMappingManager: ProductUdCatalogMappingManager,
    private val visitManager: VisitManager,
    private val offlineRepo: OfflineRepo
) {
    suspend fun createOrUpdateAgent(agentCreateOrUpdateRequest: AgentCreateOrUpdateRequest): Agent {
        return agentManager.createOrUpdateAgent(agentCreateOrUpdateRequest)
    }

    suspend fun createOrUpdateSupplier(supplierCreateOrUpdateRequest: SupplierCreateOrUpdateRequest): Supplier {
        return supplierManager.createOrUpdateSupplier(supplierCreateOrUpdateRequest)
    }

    suspend fun createOrUpdateProduct(productCreateOrUpdateRequest: ProductCreateOrUpdateRequest): OfflineProduct {
        return offlineProductManager.createOrUpdateProduct(productCreateOrUpdateRequest)
    }

    suspend fun upsertMapping(agentSupplierMapping: AgentSupplierMappingRequest, updatedBy: String): AgentSupplierMapping {
        return agentSupplierMappingManager.createOrUpdateMapping(agentSupplierMapping, updatedBy = updatedBy)
    }

    suspend fun upsertMapping(supplierProductMapping: SupplierProductMappingRequest, updatedBy: String): SupplierProductMapping {
        return supplierProductMappingManager.createOrUpdateMapping(supplierProductMapping, updatedBy = updatedBy)
    }

    suspend fun upsertMapping(request: ProductToUdCatalogMappingRequest, updatedBy: String): ProductUdCatalogMapping {
        return productUdCatalogMappingManager.createOrUpdateMapping(request, updatedBy = updatedBy)
    }

    /**
     * Retrieves a bird's eye view of all the agents in a specific city, including their details,
     * activity, and supplier relationships for a given date.
     *
     * @param city The city for which the agent data is to be fetched.
     * @param date The date for which the agent visit details are required, in string format.
     * @return A data object containing a list of agents along with their associated details
     *         and statistics.
     */
    suspend fun getAgentsBirdsEyeView(city: City, date: String?): AgentBirdsEyeView {
        val agents = agentManager.getAgents(city)
        val agentIds = agents.map { it.id }.distinct()
        val agentSupplierMappings = agentSupplierMappingManager.getMappings(agentIds)
        val agentVisitInfo = visitManager.getAgentVisitInfo(agentIds, date)
        return AgentBirdsEyeView(
            agents = agents.map { agent ->
                AgentView(
                    id = agent.id,
                    name = agent.name,
                    phone = agent.phoneNumber,
                    lastActiveAt = agentVisitInfo[agent.id]?.lastActiveAt,
                    suppliersVisited = agentVisitInfo[agent.id]?.visitedSuppliers ?: 0,
                    suppliersAlloted = agentSupplierMappings[agent.id] ?: 0,
                    lastLocation = agentVisitInfo[agent.id]?.lastLocation
                )
            }.sortedByDescending { it.lastActiveAt }
        )
    }

    suspend fun getSuppliersBirdsEyeView(city: City, date: String?): SupplierBirdsEyeView {
        val date = TimeHelpers.getStartOfDay(date)
        val suppliers = supplierManager.getSuppliers(city)
        val supplierIds = suppliers.map { it.id }.distinct()
        val lastVisitInfo = TelemetryScope.async { visitManager.getLastVisitInfo(supplierIds, date) }
        val supplierProductCounts = TelemetryScope.async { offlineRepo.getSupplierProductCounts(supplierIds, date) }
        return SupplierBirdsEyeView(
            suppliers = suppliers.map { supplier ->
                SupplierView(
                    id = supplier.id,
                    name = supplier.name,
                    address = supplier.address,
                    geoLocation = supplier.geoLocation?.let {
                        GeoLoc(
                            lat = it.x,
                            lon = it.y,
                            acc = 100.0
                        )
                    },
                    lastVisitedAt = lastVisitInfo.await()[supplier.id],
                    totalProducts = supplierProductCounts.await()[supplier.id]?.total,
                    priceCollectedProducts = supplierProductCounts.await()[supplier.id]?.priced
                )
            }.sortedByDescending { it.totalProducts }
        )
    }

    suspend fun getProductsBirdsEyeView(city: City, date: String?): ProductBirdsEyeView {
        val date = TimeHelpers.getStartOfDay(date)
        val productIds = offlineRepo.getProducts(city)
        val pricesOfUdCatalog = offlineRepo.getPrices(productIds, date, city).sortedByDescending { it.lastCalculatedAt }
        return ProductBirdsEyeView(
            products = pricesOfUdCatalog.map { product ->
                ProductView(
                    productId = product.productId,
                    udCatalogId = product.udCatalogId,
                    priceInPaise = product.priceInPaise,
                    lastCalculatedAt = product.lastCalculatedAt
                )
            }.sortedByDescending { it.lastCalculatedAt }
        )
    }

    suspend fun getSupplierDetails(supplierId: String): Supplier {
        return supplierManager.getSupplierById(supplierId)
    }

    suspend fun getProductPricesOfSupplier(supplierId: String, date: String?): ProductPricesView {
        val date = TimeHelpers.getStartOfDay(date)
        val visitsToday = visitManager.getVisitsAtSupplier(supplierId, date)
        if (visitsToday.isEmpty()) {
            return ProductPricesView(productPrices = emptyList())
        }
        val pricesOfProducts = offlineRepo.getPricesOfVisits(visitsToday, date)
        return ProductPricesView(productPrices = pricesOfProducts)
    }

    suspend fun getVisitInfo(visitId: String): Visit {
        return visitManager.getVisitById(visitId)
    }
}

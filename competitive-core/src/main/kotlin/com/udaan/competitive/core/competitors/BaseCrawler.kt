package com.udaan.competitive.core.competitors

import com.udaan.common.utils.kotlin.logger
import com.udaan.competitive.core.manager.CrawlJobManager
import com.udaan.competitive.core.models.BrandCrawlTrigger
import com.udaan.competitive.core.models.CategoryCrawlTrigger
import com.udaan.competitive.core.models.CrawlTrigger
import com.udaan.competitive.core.models.ProductCrawlTrigger
import com.udaan.competitive.core.models.AccountCrawlTrigger
import com.udaan.competitive.core.models.CrawlTriggerType
import com.udaan.competitive.core.utils.TelemetryHelper
import com.udaan.competitive.core.utils.TrackingLabel
import com.udaan.competitive.models.City
import com.udaan.competitive.models.Competitor
import com.udaan.competitive.models.CompetitorCityJobRunStatus
import com.udaan.competitive.models.Proxy

/**
 * Base crawler class providing common functionality for competitor data crawling. Handles different
 * types of crawl triggers (brand, category) and manages crawl job lifecycles.
 *
 * @property competitor The competitor this crawler is associated with
 * @property crawlJobManager Manager for tracking and updating crawl job status
 */
abstract class BaseCrawler(
        val competitor: Competitor,
        private val crawlJobManager: CrawlJobManager
) {

    companion object {
        private val log by logger()
    }

    /**
     * Main crawling function that handles different types of crawl triggers
     *
     * @param crawlTrigger The trigger containing crawl job details
     * @param proxy Optional proxy to use for crawling
     * @throws NotImplementedError for unsupported crawl trigger types
     */
    suspend fun crawl(crawlTrigger: CrawlTrigger, proxy: Proxy?) {
        when (crawlTrigger) {
            is BrandCrawlTrigger -> handleBrandTrigger(crawlTrigger, proxy)
            is CategoryCrawlTrigger -> handleCategoryTrigger(crawlTrigger, proxy)
            is AccountCrawlTrigger -> handleAccountTrigger(crawlTrigger, proxy)
            is ProductCrawlTrigger ->
                    throw NotImplementedError(
                            "Crawl Message Handler for ${crawlTrigger::class.java.simpleName} not implemented"
                    )
        }
    }
    /**
     * Handles brand-specific crawl triggers and manages crawl job status
     *
     * @param brandCrawlTrigger Trigger containing brand crawl details
     * @param proxy Optional proxy to use for crawling
     */
    private suspend fun handleBrandTrigger(brandCrawlTrigger: BrandCrawlTrigger, proxy: Proxy?) {
        var successCount = 0
        brandCrawlTrigger.brandIds.forEach { brandId ->
            try {
                TelemetryHelper.trackEvent(
                        TrackingLabel.crawl_started,
                        labels = brandCrawlTrigger.toTelemetryMap() + mapOf("entityId" to brandId)
                )
                crawlBrand(
                        cJobID = brandCrawlTrigger.ccJobId,
                        accountId = brandCrawlTrigger.accountId,
                        brandId = brandId,
                        proxy = proxy,
                        city = brandCrawlTrigger.city
                )
                TelemetryHelper.trackEvent(
                        TrackingLabel.crawl_success,
                        labels = brandCrawlTrigger.toTelemetryMap() + mapOf("entityId" to brandId)
                )
                successCount += 1
            } catch (e: Exception) {
                TelemetryHelper.trackException(
                        e,
                        brandCrawlTrigger.toTelemetryMap() + mapOf("entityId" to brandId)
                )
                TelemetryHelper.trackEvent(
                        TrackingLabel.crawl_failed,
                        labels =
                                brandCrawlTrigger.toTelemetryMap() +
                                        mapOf("entityId" to brandId, "error" to e.message.orEmpty())
                )
            }
        }
        // if one brand fails, job is marked in error state
        val finalJobStatus =
                if (successCount != brandCrawlTrigger.brandIds.size) {
                    CompetitorCityJobRunStatus.ERROR
                } else {
                    CompetitorCityJobRunStatus.COMPLETED
                }
    }

    /**
     * Handles category-specific crawl triggers and manages crawl job status
     *
     * @param categoryCrawlTrigger Trigger containing category crawl details
     * @param proxy Optional proxy to use for crawling
     */
    private suspend fun handleCategoryTrigger(
            categoryCrawlTrigger: CategoryCrawlTrigger,
            proxy: Proxy?
    ) {
        var successCount = 0
        categoryCrawlTrigger.categoryIds.forEach { categoryId ->
            try {
                TelemetryHelper.trackEvent(
                        TrackingLabel.crawl_started,
                        labels =
                                categoryCrawlTrigger.toTelemetryMap() +
                                        mapOf("entityId" to categoryId)
                )
                crawlCategory(
                        cJobID = categoryCrawlTrigger.ccJobId,
                        accountId = categoryCrawlTrigger.accountId,
                        categoryId = categoryId,
                        proxy = proxy,
                        city = categoryCrawlTrigger.city
                )
                if (categoryCrawlTrigger.ccJobId.startsWith("PJOB")) {
                    crawlJobManager.recordV3StatusChange(
                            parentJobId = categoryCrawlTrigger.ccJobId,
                            accountId = categoryCrawlTrigger.accountId,
                            categoryId = categoryId,
                            jobRunStatus = CompetitorCityJobRunStatus.COMPLETED
                    )
                }
                TelemetryHelper.trackEvent(
                        TrackingLabel.crawl_success,
                        labels =
                                categoryCrawlTrigger.toTelemetryMap() +
                                        mapOf("entityId" to categoryId)
                )
                successCount += 1
            } catch (e: Exception) {
                log.error("Unable to complete crawl ${categoryId} ${e.message}")
                e.printStackTrace()
                TelemetryHelper.trackException(
                        e,
                        labels =
                                categoryCrawlTrigger.toTelemetryMap() +
                                        mapOf("entityId" to categoryId)
                )
                TelemetryHelper.trackEvent(
                        trackingLabels = TrackingLabel.crawl_failed,
                        labels =
                                categoryCrawlTrigger.toTelemetryMap() +
                                        mapOf(
                                                "entityId" to categoryId,
                                                "exception" to e.message.orEmpty()
                                        )
                )
            }
        }
    }

    /**
     * Crawls data for a specific brand
     *
     * @param cJobID Unique identifier for the crawl job
     * @param brandId ID of the brand to crawl
     * @param accountId Account identifier for authentication
     * @param city City for which to crawl brand data
     * @param proxy Optional proxy to use for crawling
     */
    abstract suspend fun crawlBrand(
            cJobID: String,
            brandId: String,
            accountId: String,
            city: City,
            proxy: Proxy?
    )

    /**
     * Crawls data for a specific category
     *
     * @param cJobID Unique identifier for the crawl job
     * @param categoryId ID of the category to crawl
     * @param accountId Account identifier for authentication
     * @param city City for which to crawl category data
     * @param proxy Optional proxy to use for crawling
     */
    abstract suspend fun crawlCategory(
            cJobID: String,
            categoryId: String,
            accountId: String,
            city: City,
            proxy: Proxy?
    )

    /**
     * Crawls data with specific account details, such as page ID and type.
     *
     * @param cJobID Unique identifier for the crawl job
     * @param accountId Account identifier for authentication
     * @param city City for which to crawl account data
     * @param proxy Optional proxy to use for crawling
     * @param pageId Optional page identifier to crawl specific page
     * @param pageType Type of page to crawl (e.g., CATEGORY, BRAND, PRODUCT)
     */
    suspend fun crawlPage(
        cJobID: String,
        accountId: String,
        city: City,
        proxy: Proxy?,
        pageId: String,
        pageType: CrawlTriggerType
    ) {
        log.info("Crawling account: $accountId in city: ${city.name} for page: $pageId of type: $pageType")
        when (pageType) {
            CrawlTriggerType.CATEGORY -> {
                crawlCategory(cJobID, pageId, accountId, city, proxy)
            }
            CrawlTriggerType.BRAND -> {
                crawlBrand(cJobID, pageId, accountId, city, proxy)
            }
            CrawlTriggerType.PRODUCT -> {
                crawlProduct(cJobID, pageId, accountId, city, proxy)
            }
            else -> {
                log.warn("Unknown page type: $pageType, skipping crawl...")
            }
        }
    }
    /**
     * Handles account-specific crawl triggers and manages crawl job status
     *
     * @param accountCrawlTrigger Trigger containing account crawl details
     * @param proxy Optional proxy to use for crawling
     */
    private suspend fun handleAccountTrigger(
        accountCrawlTrigger: AccountCrawlTrigger,
        proxy: Proxy?
    ) {
        var successCount = 0
        if (accountCrawlTrigger.pages.isEmpty()) {
            log.info("No specific pages provided for account crawl ${accountCrawlTrigger.accountId}, skipping crawl")
            return
        } else {
            log.info("Crawling ${accountCrawlTrigger.pages.size} pages for account ${accountCrawlTrigger.accountId}")
            accountCrawlTrigger.pages.forEach { page ->
                try {
                    val telemetryMap = accountCrawlTrigger.toTelemetryMap() +
                        mapOf("pageId" to page.pageId, "pageType" to page.pageType.name)
                    TelemetryHelper.trackEvent(
                        TrackingLabel.crawl_started,
                        labels = telemetryMap
                    )
                    crawlPage(
                        cJobID = accountCrawlTrigger.ccJobId,
                        accountId = accountCrawlTrigger.accountId,
                        city = accountCrawlTrigger.city,
                        proxy = proxy,
                        pageId = page.pageId,
                        pageType = page.pageType
                    )
                    TelemetryHelper.trackEvent(
                        TrackingLabel.crawl_success,
                        labels = telemetryMap
                    )
                    successCount += 1
                } catch (e: Exception) {
                    val telemetryMap = accountCrawlTrigger.toTelemetryMap() +
                        mapOf("pageId" to page.pageId, "pageType" to page.pageType.name)
                    TelemetryHelper.trackException(
                        e,
                        labels = telemetryMap
                    )
                    TelemetryHelper.trackEvent(
                        trackingLabels = TrackingLabel.crawl_failed,
                        labels = telemetryMap +
                            mapOf("exception" to e.message.orEmpty())
                    )
                }
            }
        }
    }

    fun crawlProduct(cJobID: String, pageId: String, accountId: String, city: City, proxy: Proxy?) {
        throw NotImplementedError("crawlProduct not implemented")
    }

}

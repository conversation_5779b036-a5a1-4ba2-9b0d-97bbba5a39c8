package com.udaan.competitive.core.providers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.competitive.core.competitors.LoginClient
import com.udaan.competitive.core.competitors.hyperpure.HyperPureLoginClient
import com.udaan.competitive.core.competitors.jumbotail.JumbotailLoginClient
import com.udaan.competitive.core.competitors.metro.MetroLoginClient
import com.udaan.competitive.core.competitors.shikhar.ShikharLoginClient
import com.udaan.competitive.core.dao.CompetitorLoginAccountDao
import com.udaan.competitive.core.dao.OnlineCompSessionDao
import com.udaan.competitive.core.generateId
import com.udaan.competitive.models.Competitor
import com.udaan.competitive.models.CompetitorLoginAccount
import com.udaan.competitive.models.CompetitorLoginAccountState
import com.udaan.competitive.models.CompetitorLoginSessionState
import com.udaan.competitive.models.CompetitorSpecificLoginAttributes
import com.udaan.competitive.models.MetroLoginAttributes
import com.udaan.competitive.core.models.OTPGenerateResponse
import com.udaan.competitive.core.models.OTPLoginRequest
import com.udaan.competitive.core.models.CompetitorLoginFormResponse
import com.udaan.competitive.core.utils.TimeHelpers

/**
 * Provider class responsible for managing competitor login operations. Handles OTP generation,
 * verification, and login session management for different competitors.
 *
 * @property shikharLoginClient Client for handling Shikhar competitor login operations
 * @property jumbotailLoginClient Client for handling Jumbotail competitor login operations
 */
@Singleton
class LoginProvider
@Inject
constructor(
    private val shikharLoginClient: ShikharLoginClient,
    private val jumbotailLoginClient: JumbotailLoginClient,
    private val metroLoginClient: MetroLoginClient,
    private val hyperPureLoginClient: HyperPureLoginClient,
    private val onlineCompSessionDao: OnlineCompSessionDao,
    private val competitorLoginAccountDao: CompetitorLoginAccountDao
) {
    private val LOG by logger()

    /**
     * Gets the appropriate login client for a given competitor
     *
     * @param competitor The competitor to get login client for
     * @return [LoginClient] The login client for the specified competitor
     * @throws IllegalArgumentException if no login client exists for the competitor
     */
    private fun getLoginClient(competitor: Competitor): LoginClient =
        when (competitor) {
            Competitor.SHIKHAR -> shikharLoginClient
            Competitor.JUMBOTAIL -> jumbotailLoginClient
            Competitor.METRO -> metroLoginClient
            Competitor.HYPERPURE -> hyperPureLoginClient
            else ->
                throw IllegalArgumentException(
                    "No LoginClient available for competitor: $competitor"
                )
        }

    /**
     * Generates OTP for competitor login and manages login session creation
     *
     * @param otpLoginRequest The request containing login details and account information
     * @return [OTPGenerateResponse] Response containing OTP generation status and session ID
     */
    suspend fun generateOtpForLogin(otpLoginRequest: OTPLoginRequest): OTPGenerateResponse {
        LOG.info("Generating OTP for login request: $otpLoginRequest")
        val competitor = Competitor.valueOf(otpLoginRequest.competitor)
        val competitorCityId = "${otpLoginRequest.competitor}_${otpLoginRequest.city}"
        val competitorAttributes: CompetitorSpecificLoginAttributes? = if (competitor == Competitor.METRO) {
            val pincode = otpLoginRequest.competitorSpecificAttributes?.get("pincode") as? String
                ?: throw IllegalArgumentException("Pincode required for METRO login")
            MetroLoginAttributes(pincode)
        } else {
            null
        }
        val account =
            competitorLoginAccountDao.getLoginAccountsByCompetitorCityAndIdentifier(
                competitorCityId,
                otpLoginRequest.accountIdentifier
            )
                ?: competitorLoginAccountDao.createOrUpdate(
                    CompetitorLoginAccount(
                        id = generateId("CLA"),
                        competitorCityId = competitorCityId,
                        accountIdentifier = otpLoginRequest.accountIdentifier,
                        accountIdentifierType =
                            otpLoginRequest.accountIdentifierType,
                        supportedLoginMethods =
                            listOf(otpLoginRequest.accountLoginMethod),
                        competitorSpecificLoginAttributes = competitorAttributes,
                        state = CompetitorLoginAccountState.ACTIVE,
                        createdAt = TimeHelpers.getNow(),
                        updatedAt = TimeHelpers.getNow()
                    )
                )

        val latestSession = onlineCompSessionDao.getLatestActiveByAccountId(account.id)
        val isSessionActiveOrInProgress =
            latestSession?.state in
                listOf(
                    CompetitorLoginSessionState.LOGIN_IN_PROGRESS,
                    CompetitorLoginSessionState.ACTIVE
                )
        if (isSessionActiveOrInProgress) {
            if (latestSession != null) {
                return OTPGenerateResponse(success = true, loginSessionId = latestSession.id)
            }
        }

        val session =
            getLoginClient(competitor)
                .generateOTP(
                    otpLoginRequest.accountIdentifier,
                    account,
                    otpLoginRequest.accountLoginMethod,
                    otpLoginRequest.accountPassword
                )

        return if (session != null) {
            OTPGenerateResponse(success = true, loginSessionId = session.id)
        } else {
            OTPGenerateResponse(success = false, loginSessionId = null)
        }
    }

    /**
     * Verifies OTP and completes the login process
     *
     * @param loginSessionId ID of the login session to verify
     * @param otp One-time password to verify
     * @return [Boolean] true if verification and login were successful, false otherwise
     */
    suspend fun verifyOtpAndLogin(loginSessionId: String, otp: String): Boolean {
        val session =
            onlineCompSessionDao.getById(loginSessionId)
                ?: return false.also {
                    LOG.warn("Login session not found for ID: $loginSessionId")
                }
        val competitor = Competitor.valueOf(session.competitorCityId.split("_")[0])
        val newSession = getLoginClient(competitor).verifyOtpAndLogin(otp, session)
        return if (newSession != null) {
            LOG.info("OTP verification successful for session: $loginSessionId")
            true
        } else {
            LOG.warn("OTP verification failed for session: $loginSessionId")
            false
        }
    }

    /**
     * Retrieves the login form configuration for a specific competitor
     *
     * @param competitor The competitor to get the login form for
     * @return [CompetitorLoginFormResponse] The login form configuration for the specified competitor
     * @throws IllegalArgumentException if no login form configuration exists for the competitor
     */
    fun getCompetitorLoginForm(competitor: Competitor): CompetitorLoginFormResponse {
        val config = loginFormConfigs[competitor]
            ?: throw IllegalArgumentException("Unsupported competitor: $competitor")

        return CompetitorLoginFormResponse(
            competitor = competitor,
            accountIdentifierType = config.accountIdentifierType,
            accountLoginMethod = config.accountLoginMethod,
            fields = config.fields
        )
    }
}

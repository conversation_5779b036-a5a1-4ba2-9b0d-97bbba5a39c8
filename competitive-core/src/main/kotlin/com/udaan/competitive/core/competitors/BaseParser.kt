package com.udaan.competitive.core.competitors

import com.udaan.competitive.core.dao.CompetitorCatalogRepository
import com.udaan.competitive.models.City
import com.udaan.competitive.models.Competitor
import com.udaan.competitive.models.CompetitorCatalog
import kotlin.reflect.full.memberProperties
import org.slf4j.Logger

/**
 * Base class for parsing competitor responses and managing catalog entries. Provides common
 * functionality for parsing and persisting competitor data.
 *
 * @property competitorCatalogRepository Repository for managing competitor catalog entries
 * @property competitor The competitor associated with this parser
 */
abstract class BaseParser(
        private val competitorCatalogRepository: CompetitorCatalogRepository,
        competitor: Competitor
) {

    protected abstract val logger: Logger

    /**
     * Parses the competitor response and records the catalog entries in the database. This method
     * should be implemented by concrete parsers to handle competitor-specific parsing logic.
     *
     * @param city The city for which the data needs to be parsed and recorded
     * @param jobrunId The unique identifier for the current job run
     * @param packagedCompetitorResponse The competitor response containing the raw data to be
     * parsed
     */
    abstract suspend fun parseAndRecord(
            city: City,
            jobrunId: String,
            packagedCompetitorResponse: PackagedCompetitorResponse
    )

    protected suspend fun findOrUpsertCatalogEntry(
            newCatalog: CompetitorCatalog
    ): CompetitorCatalog {

        val existingCatalogList =
                competitorCatalogRepository.getCatalogItemForCityWithoutIdentifierType(
                        city = newCatalog.city,
                        competitor = newCatalog.competitor,
                        identifier = newCatalog.identifier,
                        variant = null
                )

        return when {
            existingCatalogList.isEmpty() -> {
                logger.info("No existing catalog found for ${newCatalog.identifier}")
                competitorCatalogRepository.createOrUpdate(newCatalog)
            }
            existingCatalogList.size > 1 -> {
                throw DuplicateCatalogException(
                        "Multiple catalogs found for ${newCatalog.identifier} in city ${newCatalog.city}"
                )
            }
            else -> {
                val existingCatalog = existingCatalogList.first()
                val needsUpdating = needsUpdatingValue(existingCatalog, newCatalog)
                if (needsUpdating) {
                    logger.info("Updating catalog for ${newCatalog.identifier}")
                    competitorCatalogRepository.createOrUpdate(
                            newCatalog.copy(id = existingCatalog.id)
                    )
                } else {
                    logger.info("Catalog for ${newCatalog.identifier} is already up to date")
                    existingCatalog
                }
            }
        }
    }

    private fun needsUpdatingValue(
            existingCatalog: CompetitorCatalog,
            newCatalog: CompetitorCatalog
    ): Boolean {
        return CompetitorCatalog::class
                .memberProperties
                .filter { it.name != "id" }
                .mapNotNull { prop ->
                    val oldValue = prop.get(existingCatalog)
                    val newValue = prop.get(newCatalog)
                    if (oldValue != newValue) {
                        "${prop.name}: $oldValue -> $newValue"
                    } else null
                }
                .isEmpty()
    }
}

/**
 * Exception thrown when multiple catalog entries are found for a single identifier in a city.
 *
 * @property message The error message describing the duplicate entries
 */
class DuplicateCatalogException(message: String) : RuntimeException(message)

package com.udaan.competitive.core.competitors.jumbotail

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.common.utils.kotlin.logger
import com.udaan.competitive.core.competitors.BaseCrawler
import com.udaan.competitive.core.dao.OnlineCompSessionDao
import com.udaan.competitive.core.manager.CrawlJobManager
import com.udaan.competitive.core.manager.NotificationManager
import com.udaan.competitive.core.manager.SlackUrl
import com.udaan.competitive.core.models.*
import com.udaan.competitive.core.providers.EventEmitterProvider
import com.udaan.competitive.core.providers.Message
import com.udaan.competitive.models.*
import javax.ws.rs.NotFoundException

@Singleton
class JumbotailCrawler @Inject constructor(
    private val crawlJobManager: CrawlJobManager,
    private val eventEmitterProvider: EventEmitterProvider,
    private val onlineCompSessionDao: OnlineCompSessionDao
): BaseCrawler(Competitor.JUMBOTAIL, crawlJobManager) {

    companion object {
        private val log by logger()
    }

    private fun CompetitorLoginSession.toAuthorization(): AccessToken? {
        val sessionAttributes = this.competitorSpecificAttributes as? JTSessionAttributes ?: return null
        if (sessionAttributes.customerId == null || authToken == null) {
            throw NotFoundException("CustomerId ${sessionAttributes.customerId} / authToken can't be null")
        }
        val authTokenWithPrefix = when {
            authToken!!.startsWith("Bearer") -> authToken!!
            else -> "Bearer $authToken"
        }
        return AccessToken(
            userId = Pair(CompetitorSpecificAttributes.USER_ID.key, sessionAttributes.customerId!!), accessToken = Pair(
                CompetitorSpecificAttributes.AUTH_TOKEN.key, authTokenWithPrefix
            ), mobile = 100 //Adding a dummy value as this is not required for JT
        )
    }

    override suspend fun crawlBrand(cJobID: String, brandId: String, accountId: String, city: City, proxy: Proxy?) {
        log.info("crawlByBrandNew cJobID:$cJobID on brand: $brandId with account:$accountId using proxy: ${proxy?.id}")
        val loginSession = onlineCompSessionDao.getLatestActiveByAccountId(accountId)
        val authorization =
            loginSession?.toAuthorization() ?: throw NotFoundException("Authorization couldn't be found for $accountId")

        val jumbotailclient = JumbotailClient(
            authToken = authorization.accessToken.second,
            customerId = authorization.userId.second,
            proxy = proxy
        )

        log.info("Crawling brand: $brandId")
        val crawledCount = try {
            val response = jumbotailclient.getBrandProducts(brandId).await()
            emitResponse(
                cJobID = cJobID,
                city = city,
                subCategoryRes = response ?: throw Exception("Failed to get response for $brandId")
            )
        } catch (e: Throwable) {
            log.warn("Brand Request Failed Brand: $brandId message: ${e.message}")
            NotificationManager.sendSlackNotification(SlackUrl.COMPETITIVEALERTS, "message: ${e.message}")
            0
        }

        log.info("Total product crawled on $cJobID using $accountId - $crawledCount")
    }

    override suspend fun crawlCategory(
        cJobID: String,
        categoryId: String,
        accountId: String,
        city: City,
        proxy: Proxy?
    ) {
        log.info("crawlByBrandNew cJobID:$cJobID on brand: $categoryId with account:$accountId using proxy: ${proxy?.id}")
        val loginSession = onlineCompSessionDao.getLatestActiveByAccountId(accountId)
        val authorization =
            loginSession?.toAuthorization() ?: throw NotFoundException("Authorization couldn't be found for $accountId")

        val jumbotailclient = JumbotailClient(
            authToken = authorization.accessToken.second,
            customerId = authorization.userId.second,
            proxy = proxy
        )

        log.info("Crawling category: $categoryId")
        try {
            val response = jumbotailclient.getCategoryProducts(categoryId).await()
            emitResponse(
                cJobID = cJobID,
                city = city,
                subCategoryRes = response ?: throw Exception("Failed to get response for $categoryId")
            )
        } catch (e: Throwable) {
            log.warn("Category Request Failed Brand: $categoryId $accountId message: ${e.message}")
            NotificationManager.sendSlackNotification(SlackUrl.COMPETITIVEALERTS, "message: ${e.message}")
        }
    }

    private suspend fun emitResponse(cJobID: String, city: City, subCategoryRes: JTProductResultsPage) {
        val parsePayload = ParsePayload(
            competitor = Competitor.JUMBOTAIL,
            city = city,
            jobRunId = cJobID,
            response = PackagedJTProductResultsPage(subCategoryRes)
        )
        return eventEmitterProvider.sendMessages(
            ProcessorQueues.ParserQueue, listOf(
                Message(
                    id = null,
                    payload = parsePayload
                )
            )
        )
    }
}

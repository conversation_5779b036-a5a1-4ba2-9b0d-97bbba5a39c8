package com.udaan.competitive.core.dao

import com.google.inject.Inject
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.udaan.competitive.core.models.CrawlJobRun
import com.udaan.competitive.core.utils.NamedConstants
import com.udaan.competitive.models.CompetitorCityJobRunStatus
import com.udaan.instrumentation.Telemetry
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.jdbi.v3.core.Jdbi
import java.sql.Timestamp

@Singleton
class CrawlJobRunDao @Inject constructor(
    @Named(NamedConstants.DB.ONLINE_COMP) private val dbi: Jdbi
) {

    companion object {
        const val TABLE_NAME = "job_runs"
    }

    suspend fun create(jobRun: CrawlJobRun) {
        return withContext(Dispatchers.IO) {
            dbi.withHandle<Unit, Exception> { handle ->
                handle.createUpdate(
                    """ 
                    INSERT INTO $TABLE_NAME (id, config_id, parent_job_id, status, created_at, run_after, run_before, crawled_at, triggered_by, remarks) 
                    VALUES (:id, :configId, :parentJobId, :status, :createdAt, :runAfter, :runBefore, :crawledAt, :triggeredBy, :remarks) 
                    """.trimIndent()
                )
                    .bind("id", jobRun.id)
                    .bind("configId", jobRun.configId)
                    .bind("parentJobId", jobRun.parentJobId)
                    .bind("status", jobRun.status)
                    .bind("createdAt", jobRun.createdAt)
                    .bind("runAfter", jobRun.runAfter)
                    .bind("runBefore", jobRun.runBefore)
                    .bind("crawledAt", jobRun.crawledAt)
                    .bind("triggeredBy", jobRun.triggeredBy)
                    .bind("remarks", "no remarks")
                    .execute()
            }
        }
    }

    suspend fun createBatch(jobRuns: List<CrawlJobRun>, batchSize: Int = 100) {
        if (jobRuns.isEmpty()) return
        
        return withContext(Dispatchers.IO) {
            dbi.withHandle<Unit, Exception> { handle ->
                jobRuns.chunked(batchSize).forEach { chunk ->
                    try {
                        val batch = handle.prepareBatch(
                            """ 
                            INSERT INTO $TABLE_NAME (id, config_id, parent_job_id, status, created_at, run_after, run_before, crawled_at, triggered_by, remarks) 
                            VALUES (:id, :configId, :parentJobId, :status, :createdAt, :runAfter, :runBefore, :crawledAt, :triggeredBy, :remarks) 
                            """.trimIndent()
                        )

                        chunk.forEach { jobRun ->
                            batch.bind("id", jobRun.id)
                                .bind("configId", jobRun.configId)
                                .bind("parentJobId", jobRun.parentJobId)
                                .bind("status", jobRun.status)
                                .bind("createdAt", jobRun.createdAt)
                                .bind("runAfter", jobRun.runAfter)
                                .bind("runBefore", jobRun.runBefore)
                                .bind("crawledAt", jobRun.crawledAt)
                                .bind("triggeredBy", jobRun.triggeredBy)
                                .bind("remarks", "no remarks")
                                .add()
                        }

                        batch.execute()
                    } catch (e: Exception) {
                        Telemetry.trackException(e, mapOf("chunkSize" to chunk.size.toString()))
                    }
                }
            }
        }
    }

    suspend fun changeStatus(
        configId: String,
        status: CompetitorCityJobRunStatus,
        crawledAt: Timestamp?,
        parentJobId: String
    ) {
        return withContext(Dispatchers.IO) {
            dbi.withHandle<Unit, Exception> { handle ->
                handle.createUpdate(
                    """
                UPDATE $TABLE_NAME
                SET crawled_at = :crawledAt, status = :status
                WHERE id = (
                    SELECT id FROM $TABLE_NAME
                    WHERE config_id = :configId
                    AND parent_job_id = :parentJobId
                    ORDER BY created_at DESC
                    LIMIT 1
                )
                """
                )
                    .bind("status", status.name)
                    .bind("parentJobId", parentJobId)
                    .apply {
                        if (crawledAt != null) bind("crawledAt", crawledAt)
                    }
                    .bind("configId", configId)
                    .execute()
            }
        }
    }

    suspend fun getLastCrawls(configId: String, limit: Int): List<CrawlJobRun> {
        return withContext(Dispatchers.IO) {
            dbi.withHandle<List<CrawlJobRun>, Exception> { handle ->
                handle.createQuery(
                    """
                    SELECT * FROM $TABLE_NAME
                    WHERE config_id = :configId
                    ORDER BY created_at DESC
                    LIMIT :limit
                    """
                )
                    .bind("configId", configId)
                    .bind("limit", limit)
                    .mapTo(CrawlJobRun::class.java)
                    .list()
            }
        }
    }

    suspend fun getAccountIdForParentJob(parentJobRunId: String): String? {
        return withContext(Dispatchers.IO) {
            dbi.withHandle<String, Exception> { handle ->
                handle.createQuery(
                    """
                    SELECT account_id as account_id FROM $TABLE_NAME
                    JOIN comp_pages ON comp_pages.id = config_id
                    WHERE parent_job_id = :parentJobRunId
                    """
                )
                    .bind("parentJobRunId", parentJobRunId)
                    .map { row ->
                        row.getColumn("account_id", String::class.java)
                    }
                    .firstOrNull()
            }
        }
    }
}